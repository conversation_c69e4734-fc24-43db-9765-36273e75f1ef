import{j as e,r as t}from"./react-core-B9nwsbCA.js";import{u as s,y as r,z as a,A as i,s as n,B as o,C as l,D as d,E as c,F as u,G as m,H as h,I as p}from"./utils-DghTBY4T.js";import{I as g,B as x}from"./ui-components-Car30pze.js";import{c as b}from"./state-4gBW_4Nx.js";import{F as f,j as v,e as j,d as y,p as w,q as N,X as k,n as E,I as S,U as P}from"./icons-BWE0bDFO.js";import{u as U}from"./router-BDggJ1ol.js";import"./supabase-C-51sAsE.js";const C="https://www.bluefilmx.com",T="/media/uploads/upload.php",L=104857600,R=5242880;function z(){const{user:e}=s.getState();return e&&e.id||null}async function A(e,t,s){if(!z())throw new Error("Authentication required");if(e.size>L)throw new Error(`File too large. Maximum size: ${L/1024/1024}MB`);return e.size>R?async function(e,t,s){const r=z();if(!r)throw new Error("Authentication required");const a=R,i=Math.ceil(e.size/a);let n=0;for(let l=0;l<i;l++){const d=l*a,c=Math.min(d+a,e.size),u=e.slice(d,c),m=new FormData;m.append("file",u,e.name),m.append("type",t),m.append("chunkIndex",l.toString()),m.append("totalChunks",i.toString());try{const t=await I(m,r);if(n+=u.size,s&&s({loaded:n,total:e.size,percentage:Math.round(n/e.size*100)}),l===i-1)return t}catch(o){throw new Error(`Chunk upload failed: ${o instanceof Error?o.message:"Unknown error"}`)}}throw new Error("Chunked upload completed but no final response received")}(e,t,s):async function(e,t,s){const r=z();if(!r)throw new Error("Authentication required");const a=new FormData;return a.append("file",e),a.append("type",t),new Promise(((e,t)=>{const i=new XMLHttpRequest;i.upload.addEventListener("progress",(e=>{e.lengthComputable&&s&&s({loaded:e.loaded,total:e.total,percentage:Math.round(e.loaded/e.total*100)})})),i.addEventListener("load",(()=>{if(200===i.status)try{const s=JSON.parse(i.responseText);s.success?e(s):t(new Error(s.error||"Upload failed"))}catch(s){t(new Error("Invalid response from server"))}else try{const e=JSON.parse(i.responseText);t(new Error(e.error||`Upload failed with status ${i.status}`))}catch{t(new Error(`Upload failed with status ${i.status}`))}})),i.addEventListener("error",(()=>{t(new Error("Network error during upload"))})),i.addEventListener("timeout",(()=>{t(new Error("Upload timeout"))})),i.open("POST",C+T),i.setRequestHeader("Authorization",`Bearer ${r}`),i.timeout=3e5,i.send(a)}))}(e,t,s)}function I(e,t){return new Promise(((s,r)=>{const a=new XMLHttpRequest;a.addEventListener("load",(()=>{if(200===a.status)try{const e=JSON.parse(a.responseText);e.success?s(e):r(new Error(e.error||"Chunk upload failed"))}catch(e){r(new Error("Invalid response from server"))}else try{const e=JSON.parse(a.responseText);r(new Error(e.error||`Chunk upload failed with status ${a.status}`))}catch{r(new Error(`Chunk upload failed with status ${a.status}`))}})),a.addEventListener("error",(()=>{r(new Error("Network error during chunk upload"))})),a.addEventListener("timeout",(()=>{r(new Error("Chunk upload timeout"))})),a.open("POST",C+T),a.setRequestHeader("Authorization",`Bearer ${t}`),a.timeout=6e4,a.send(e)}))}const F=b(((e,t)=>({isUploading:!1,uploadProgress:0,uploadError:null,uploadedVideoUrl:null,uploadedThumbnailUrl:null,setUploadProgress:t=>{e({uploadProgress:t})},resetUpload:()=>{e({isUploading:!1,uploadProgress:0,uploadError:null,uploadedVideoUrl:null,uploadedThumbnailUrl:null})},uploadVideo:async o=>{const{title:l,description:d,category:c,tags:u,videoFile:m,thumbnailFile:h}=o,{user:p}=s.getState();if(!p)throw new Error("User must be logged in to upload videos");e({isUploading:!0,uploadProgress:0,uploadError:null,uploadedVideoUrl:null,uploadedThumbnailUrl:null});try{const s=r(m,"video");if(!s.isValid)throw new Error(s.error||"Invalid video file");if(h){const e=r(h,"image");if(!e.isValid)throw new Error(e.error||"Invalid thumbnail file")}await a(),i(),m.size;let o;e({uploadProgress:5});try{o=(await A(m,"video",(t=>{const s=Math.round(.75*t.percentage);e({uploadProgress:5+s})}))).url}catch(g){throw new Error(`Video upload failed: ${g instanceof Error?g.message:"Unknown error"}`)}e({uploadProgress:85,uploadedVideoUrl:o});let u=null;if(h)try{u=(await A(h,"image",(t=>{const s=Math.round(.1*t.percentage);e({uploadProgress:85+s})}))).url}catch(g){}e({uploadProgress:95,uploadedThumbnailUrl:u});try{const s={title:l,description:d,video_url:o,thumbnail_url:u,user_id:p.id,category:c,is_hd:!0},{data:r,error:a}=await n.from("videos").insert(s).select().single();if(a)throw new Error(`Failed to save video metadata: ${a.message}`);return e({uploadProgress:100}),setTimeout((()=>{t().resetUpload()}),2e3),r.id}catch(g){throw new Error(`Failed to save video metadata: ${g instanceof Error?g.message:"Unknown error"}`)}}catch(g){const t=g instanceof Error?g.message:"Upload failed";throw e({isUploading:!1,uploadError:t,uploadProgress:0}),g}}}))),M=[{id:"1",name:"Hot",slug:"hot",icon:e.jsx(f,{size:16,className:"text-red-500"})},{id:"2",name:"Trending",slug:"trending",icon:e.jsx(v,{size:16,className:"text-blue-500"})},{id:"3",name:"New",slug:"new",icon:e.jsx(j,{size:16,className:"text-green-500"})}],O=({onSelect:t,selectedCategory:s="new"})=>e.jsxs("div",{className:"mb-6",children:[e.jsx("label",{className:"block text-sm font-medium text-white mb-2",children:"Category"}),e.jsx("div",{className:"grid grid-cols-3 gap-3",children:M.map((r=>e.jsxs("button",{type:"button",className:"flex flex-col items-center justify-center p-3 rounded-md transition-colors "+(s===r.slug?"bg-gray-700 border-2 border-blue-700":"bg-gray-800 border border-gray-700 hover:border-gray-600"),onClick:()=>t(r.slug),children:[e.jsx("div",{className:"mb-2",children:r.icon}),e.jsx("span",{className:"text-sm font-medium",children:r.name})]},r.id)))})]}),V=()=>{const r=U(),a=t.useRef(null),i=t.useRef(null),[n,b]=t.useState(""),[f,v]=t.useState(""),[j,C]=t.useState("new"),[T,L]=t.useState(""),[R,z]=t.useState(null),[A,I]=t.useState(null),[M,V]=t.useState(null),[$,q]=t.useState(null),[D,H]=t.useState([]),[B,Y]=t.useState(-1),[_,G]=t.useState(!1),[W,J]=t.useState({}),[K,X]=t.useState(null),[Q,Z]=t.useState(null),{uploadVideo:ee,isUploading:te,uploadProgress:se,error:re}=F(),ae=t.useCallback((e=>{F.setState({error:e})}),[]);t.useEffect((()=>{const e=o();if(Z(e),e.isSupported||ae(`Your browser is missing required features: ${e.missingFeatures.join(", ")}`),l()){d();const e=setInterval((()=>{d()}),3e4);return()=>clearInterval(e)}}),[ae]),t.useEffect((()=>{te&&X(null)}),[te]);const ie=e=>{const t=e.target.files?.[0];if(t){const e=c(t);if(!e.isValid)return void J({...W,video:e.error||"Invalid video file"});z(t);try{const e=URL.createObjectURL(t);V(e)}catch(s){}if(H([]),Y(-1),W.video){const e={...W};delete e.video,J(e)}}};return e.jsxs("div",{className:"space-y-6",children:[(l()||/iPad|iPhone|iPod/.test(navigator.userAgent))&&e.jsx("div",{className:"p-4 bg-blue-900/50 border border-blue-700 rounded-lg",children:e.jsxs("div",{className:"flex items-start space-x-3",children:[e.jsx(y,{size:20,className:"text-blue-400 mt-0.5 flex-shrink-0"}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-sm font-medium text-blue-200 mb-1",children:"Mobile Upload Tips"}),e.jsxs("div",{className:"text-sm text-blue-300 space-y-1",children:[e.jsx("p",{children:"• Use MP4 format for best compatibility"}),e.jsx("p",{children:"• Keep files under 60MB for faster uploads"}),e.jsx("p",{children:"• Ensure stable internet connection"}),e.jsx("p",{children:"• Large files will be uploaded in chunks for reliability"}),e.jsx("p",{children:"• Stay on this page during upload to prevent logout"}),e.jsx("p",{children:"• If you experience logout issues during thumbnail selection, try refreshing the page and uploading without auto-generated thumbnails"})]})]})]})}),"undefined"!=typeof navigator&&navigator.connection&&(()=>{const t=navigator.connection;return"slow-2g"===t.effectiveType||"2g"===t.effectiveType?e.jsx("div",{className:"p-4 bg-yellow-900/50 border border-yellow-700 rounded-lg",children:e.jsxs("div",{className:"flex items-start space-x-3",children:[e.jsx(y,{size:20,className:"text-yellow-400 mt-0.5 flex-shrink-0"}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-sm font-medium text-yellow-200 mb-1",children:"Slow Connection Detected"}),e.jsx("p",{className:"text-sm text-yellow-300",children:"Your connection appears to be slow. Consider uploading smaller files or waiting for a better connection."})]})]})}):null})(),Q&&!Q.isSupported&&e.jsx("div",{className:"p-4 bg-red-900/50 border border-red-700 rounded-lg",children:e.jsxs("div",{className:"flex items-start space-x-3",children:[e.jsx(y,{size:20,className:"text-red-400 mt-0.5 flex-shrink-0"}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-sm font-medium text-red-200 mb-1",children:"Browser Compatibility Issue"}),e.jsxs("p",{className:"text-sm text-red-300",children:["Your browser is missing: ",Q.missingFeatures.join(", "),". Please update your browser or try a different one."]})]})]})}),e.jsxs("form",{onSubmit:async e=>{if(e.preventDefault(),!(()=>{const e={};return n.trim()||(e.title="Title is required"),f.trim()||(e.description="Description is required"),R||(e.video="Video file is required"),J(e),0===Object.keys(e).length})())return;if(!R)return;const t=T.split(",").map((e=>e.trim())).filter((e=>e.length>0));try{ae(null);const e=await ee({title:n,description:f,category:j,tags:t,videoFile:R,thumbnailFile:A});e?(X("Video uploaded successfully! Redirecting to video page..."),setTimeout((()=>{r(`/video/${e}`)}),2e3)):re||ae("Upload failed. Please try again.")}catch(s){const e=s instanceof Error?s.message:"An unknown error occurred";ae(e)}},className:"space-y-6",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{className:"space-y-6",children:[e.jsx(g,{label:"Video Title",placeholder:"Enter a descriptive title",value:n,onChange:e=>b(e.target.value),error:W.title,fullWidth:!0}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-200 mb-1",children:"Description"}),e.jsx("textarea",{className:"w-full rounded-md bg-gray-800 border border-gray-700 text-white focus:border-orange-500 focus:ring-2 focus:ring-orange-500 focus:ring-opacity-20 p-4 transition-colors placeholder:text-gray-400 min-h-[120px] "+(W.description?"border-red-500":""),placeholder:"Describe your video",value:f,onChange:e=>v(e.target.value)}),W.description&&e.jsx("p",{className:"text-sm text-red-500 mt-1",children:W.description})]}),e.jsx(O,{selectedCategory:j,onSelect:C}),e.jsx(g,{label:"Tags",placeholder:"Enter tags separated by commas",value:T,onChange:e=>L(e.target.value),fullWidth:!0})]}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-200 mb-2",children:"Video File"}),e.jsx("input",{type:"file",ref:a,className:"hidden",accept:"video/*,video/mp4,video/webm,video/quicktime,video/x-msvideo,video/3gpp,video/x-ms-wmv",onChange:ie}),R?e.jsxs("div",{className:"relative border rounded-lg overflow-hidden h-[200px]",children:[M&&e.jsx("video",{src:M,className:"w-full h-full object-cover",controls:!0}),e.jsx("button",{type:"button",className:"absolute top-2 right-2 bg-red-500 text-white p-1 rounded-full hover:bg-red-600 transition-colors",onClick:()=>{M&&URL.revokeObjectURL(M),z(null),V(null),a.current&&(a.current.value="")},children:e.jsx(k,{size:16})})]}):e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"border-2 border-dashed rounded-lg p-6 flex flex-col items-center justify-center cursor-pointer hover:border-orange-500 transition-colors h-[200px] border-gray-700",onClick:e=>{e&&(e.preventDefault(),e.stopPropagation()),setTimeout((()=>{a.current?.click()}),100)},children:[e.jsx(w,{size:48,className:"text-gray-500 mb-3"}),e.jsx("p",{className:"text-gray-400 text-center",children:"Click to choose from gallery"}),e.jsx("p",{className:"text-gray-500 text-sm mt-1",children:"MP4, WebM or MOV (max. 50MB)"})]}),(l()||/iPad|iPhone|iPod/.test(navigator.userAgent))&&e.jsx("div",{className:"flex justify-center",children:e.jsx(x,{type:"button",variant:"secondary",size:"sm",leftIcon:e.jsx(N,{size:16}),onClick:e=>{e&&(e.preventDefault(),e.stopPropagation());const t=document.createElement("input");t.type="file",t.accept="video/*",t.capture="environment",t.style.position="absolute",t.style.left="-9999px",t.style.opacity="0",t.onchange=e=>{const s=e.target.files?.[0];if(s){ie({target:{files:[s]}})}document.body.removeChild(t)},document.body.appendChild(t),setTimeout((()=>{t.click()}),100)},className:"w-full sm:w-auto",children:"Record with Camera"})})]}),W.video&&e.jsx("p",{className:"text-sm text-red-500 mt-1",children:W.video})]}),e.jsxs("div",{children:[e.jsxs("div",{className:"flex justify-between items-center mb-2",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-200",children:"Thumbnail Image (Optional)"}),R&&e.jsx(x,{variant:"secondary",size:"sm",leftIcon:e.jsx(N,{size:16}),onClick:async()=>{if(R)try{G(!0);const{user:t}=s.getState();if(!t)return void J({...W,thumbnail:"Please log in again to generate thumbnails"});if(l()){const e=navigator.deviceMemory;if(e&&e<2)return void J({...W,thumbnail:"Device memory too low for thumbnail generation. Please upload a custom thumbnail instead."});await m(500)}const r=await h((()=>p(R,3)),(()=>{J({...W,thumbnail:"Authentication lost during thumbnail generation. Please refresh the page and try again."})})),{user:a}=s.getState();if(!a)return void J({...W,thumbnail:"Authentication lost during thumbnail generation. Please refresh the page and try again."});let i=[];try{i=r.map((e=>URL.createObjectURL(e))),H(i),l()&&setTimeout((()=>{i.forEach((e=>{try{URL.revokeObjectURL(e)}catch(t){}}))}),3e4)}catch(e){return void J({...W,thumbnail:"Failed to create thumbnail previews. Please try uploading a custom thumbnail."})}if(I(null),q(null),W.thumbnail){const e={...W};delete e.thumbnail,J(e)}l()&&setTimeout((()=>{const{user:e}=s.getState();e||J({...W,thumbnail:"Authentication lost. Please refresh the page and try again."})}),200)}catch(t){const{user:e}=s.getState();if(!e)return void J({...W,thumbnail:"Authentication lost during thumbnail generation. Please refresh the page and try again."});let r="Failed to generate thumbnails";t instanceof Error&&(r=t.message.includes("Authentication")?"Authentication lost. Please refresh the page and try again.":t.message.includes("memory")?"Insufficient device memory. Try uploading without custom thumbnails.":t.message.includes("timeout")?"Thumbnail generation timed out. Your device may be too slow for this operation.":t.message),J({...W,thumbnail:r})}finally{G(!1)}else J({...W,thumbnail:"Please upload a video first"})},isLoading:_,disabled:_,children:"Auto-Generate"})]}),e.jsx("input",{type:"file",ref:i,className:"hidden",accept:"image/*",onChange:e=>{const t=e.target.files?.[0];if(t){const{user:e}=s.getState();if(!e)return void J({...W,thumbnail:"Please log in again to select a thumbnail"});const a=u(t);if(!a.isValid)return void J({...W,thumbnail:a.error||"Invalid image file"});I(t);try{if(l()){const e=navigator.deviceMemory;if(e&&e<2)q(null);else{const e=URL.createObjectURL(t);q(e),setTimeout((()=>{try{URL.revokeObjectURL(e)}catch(t){}}),5e3)}}else{const e=URL.createObjectURL(t);q(e)}}catch(r){q(null)}if(W.thumbnail){const e={...W};delete e.thumbnail,J(e)}setTimeout((()=>{const{user:e}=s.getState();e||J({...W,thumbnail:"Authentication lost. Please refresh the page and try again."})}),100)}}}),D.length>0&&e.jsxs("div",{className:"mb-4",children:[e.jsx("p",{className:"text-sm text-gray-300 mb-2",children:"Select a generated thumbnail:"}),e.jsx("div",{className:"grid grid-cols-3 gap-2",children:D.map(((t,r)=>e.jsxs("div",{className:"relative border-2 rounded overflow-hidden cursor-pointer transition-all "+(B===r?"border-orange-500 scale-105":"border-gray-700 hover:border-gray-500"),onClick:()=>(async e=>{if(e>=0&&e<D.length){const{user:r}=s.getState();if(!r)return void J({...W,thumbnail:"Please log in again to select a thumbnail"});Y(e);try{const t=await fetch(D[e]),r=await t.blob(),a=new File([r],`thumbnail-${Date.now()}.jpg`,{type:"image/jpeg"});I(a),q(D[e]),setTimeout((()=>{const{user:e}=s.getState();e||J({...W,thumbnail:"Authentication lost. Please refresh the page and try again."})}),100)}catch(t){const{user:e}=s.getState();J(e?{...W,thumbnail:"Failed to select thumbnail"}:{...W,thumbnail:"Authentication lost during thumbnail selection. Please refresh the page and try again."})}}})(r),children:[e.jsx("img",{src:t,alt:`Generated thumbnail ${r+1}`,className:"w-full aspect-video object-cover"}),B===r&&e.jsx("div",{className:"absolute top-1 right-1 bg-orange-500 text-white rounded-full p-0.5",children:e.jsx(E,{size:14})})]},r)))})]}),A&&-1!==B?e.jsxs("div",{className:"relative border rounded-lg overflow-hidden h-[200px]",children:[$&&e.jsx("img",{src:$,alt:"Thumbnail preview",className:"w-full h-full object-cover"}),e.jsx("button",{type:"button",className:"absolute top-2 right-2 bg-red-500 text-white p-1 rounded-full hover:bg-red-600 transition-colors",onClick:()=>{I(null),q(null),Y(-1),i.current&&(i.current.value="")},children:e.jsx(k,{size:16})})]}):e.jsxs("div",{className:"border-2 border-dashed rounded-lg p-6 flex flex-col items-center justify-center cursor-pointer hover:border-orange-500 transition-colors h-[200px] "+(W.thumbnail?"border-red-500":"border-gray-700"),onClick:()=>{i.current?.click()},children:[e.jsx(S,{size:48,className:"text-gray-500 mb-3"}),e.jsx("p",{className:"text-gray-400 text-center",children:"Click to upload custom thumbnail"}),e.jsx("p",{className:"text-gray-500 text-sm mt-1",children:"JPG, PNG or GIF (recommended: 1280×720, max 5MB)"})]}),W.thumbnail&&e.jsx("p",{className:"text-sm text-red-500 mt-1",children:W.thumbnail})]})]})]}),re&&e.jsxs("div",{className:"bg-red-500/20 border border-red-500 rounded-md p-3 flex items-start",children:[e.jsx(y,{size:20,className:"text-red-500 mr-2 flex-shrink-0 mt-0.5"}),e.jsxs("div",{className:"text-red-200",children:[e.jsx("p",{children:re}),re.includes("Storage buckets don't exist")&&e.jsxs("div",{className:"mt-2 text-sm",children:[e.jsx("p",{children:"This is likely because:"}),e.jsxs("ul",{className:"list-disc pl-5 mt-1 space-y-1",children:[e.jsx("li",{children:"The storage buckets haven't been created in Supabase"}),e.jsx("li",{children:"Your user account doesn't have permission to access the buckets"})]}),e.jsxs("p",{className:"mt-2",children:[e.jsx("strong",{children:"Solution:"})," An administrator needs to create the 'videos' and 'thumbnails' buckets in the Supabase dashboard."]})]}),re.includes("Row Level Security (RLS) policies")&&e.jsxs("div",{className:"mt-2 text-sm",children:[e.jsx("p",{children:"This is likely because:"}),e.jsxs("ul",{className:"list-disc pl-5 mt-1 space-y-1",children:[e.jsx("li",{children:"The Row Level Security (RLS) policies for the storage buckets are not properly configured"}),e.jsx("li",{children:"Your user account doesn't have permission to upload files to these buckets"})]}),e.jsxs("p",{className:"mt-2",children:[e.jsx("strong",{children:"Solution:"})," An administrator needs to check and update the RLS policies for the storage buckets with the following SQL:"]}),e.jsx("pre",{className:"bg-gray-800 p-2 rounded mt-2 overflow-x-auto text-xs",children:"-- First, check existing policies\nSELECT policyname, tablename, cmd, qual, with_check\nFROM pg_policies\nWHERE tablename = 'objects' AND schemaname = 'storage';\n\n-- Then, update the policies if needed\nALTER POLICY \"Users can upload videos to their own folder\"\nON storage.objects\nWITH CHECK (\n  bucket_id = 'videos' AND\n  auth.uid()::text = (storage.foldername(name))[1]\n);\n\nALTER POLICY \"Users can upload thumbnails to their own folder\"\nON storage.objects\nWITH CHECK (\n  bucket_id = 'thumbnails' AND\n  auth.uid()::text = (storage.foldername(name))[1]\n);"})]}),(re.includes("violates foreign key constraint")||re.includes("user profile does not exist"))&&e.jsxs("div",{className:"mt-2 text-sm",children:[e.jsx("p",{children:"This is likely because your user profile hasn't been properly created in the database."}),e.jsxs("p",{className:"mt-2",children:[e.jsx("strong",{children:"Solution:"})," Please try the following steps:"]}),e.jsxs("ol",{className:"list-decimal pl-5 mt-1 space-y-1",children:[e.jsx("li",{children:"Log out and log back in to trigger automatic profile creation"}),e.jsx("li",{children:"Refresh the page and try uploading again"}),e.jsx("li",{children:"If the issue persists, contact support"})]})]})]})]}),K&&!re&&e.jsxs("div",{className:"bg-green-500/20 border border-green-500 rounded-md p-3 flex items-start",children:[e.jsx(E,{size:20,className:"text-green-500 mr-2 flex-shrink-0 mt-0.5"}),e.jsx("p",{className:"text-green-200",children:K})]}),te&&e.jsxs("div",{className:"bg-gray-700 rounded-full overflow-hidden",children:[e.jsx("div",{className:"bg-orange-500 h-2 transition-all duration-300",style:{width:`${se}%`}}),e.jsxs("p",{className:"text-center text-sm text-gray-400 mt-2",children:["Uploading: ",se,"%"]})]}),e.jsxs("div",{className:"flex justify-end space-x-3 pt-4",children:[e.jsx(x,{variant:"ghost",type:"button",onClick:()=>r("/"),children:"Cancel"}),e.jsx(x,{variant:"primary",type:"submit",leftIcon:e.jsx(P,{size:18}),isLoading:te,disabled:te,children:"Upload Video"})]})]})]})},$=()=>{const r=U(),{user:a,isLoading:i,isApproved:n,profile:o}=s(),[l,d]=t.useState(!1);return t.useEffect((()=>{const e=setTimeout((()=>{d(!0)}),2e3);return()=>clearTimeout(e)}),[]),t.useEffect((()=>{!l||i||a||r("/")}),[a,i,r,l]),i||!l?e.jsx("div",{className:"container mx-auto px-4 py-8",children:e.jsxs("div",{className:"flex flex-col justify-center items-center h-64",children:[e.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mb-4"}),e.jsx("p",{className:"text-gray-400 text-center",children:i?"Loading your account...":"Checking authentication..."}),e.jsx("p",{className:"text-gray-500 text-sm text-center mt-2",children:"Please wait while we verify your login status"})]})}):a?n?e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsx("h1",{className:"text-2xl md:text-3xl font-bold mb-6",children:"Upload Video"}),e.jsx("div",{className:"bg-gray-800 rounded-lg p-6",children:e.jsx(V,{})})]}):e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsx("h1",{className:"text-2xl md:text-3xl font-bold mb-6",children:"Upload Video"}),e.jsx("div",{className:"bg-gray-800 rounded-lg p-6",children:e.jsxs("div",{className:"flex flex-col items-center justify-center py-8 text-center",children:[e.jsx(y,{size:48,className:"text-yellow-500 mb-4"}),e.jsx("h2",{className:"text-xl font-semibold mb-2",children:"Approval Required"}),e.jsx("p",{className:"text-gray-400 max-w-md mb-4",children:"Your account is pending approval. New users need to be approved before they can upload videos."}),e.jsx("p",{className:"text-gray-500 text-sm",children:"Please check back later or contact an administrator for assistance."})]})})]}):null};export{$ as default};
