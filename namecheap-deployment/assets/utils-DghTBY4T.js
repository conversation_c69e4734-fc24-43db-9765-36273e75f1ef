import{r as e,a as t}from"./react-core-B9nwsbCA.js";import{c as r}from"./state-4gBW_4Nx.js";import{c as n}from"./supabase-C-51sAsE.js";var i={exports:{}},o={},a=e;var s="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},c=a.useState,l=a.useEffect,u=a.useLayoutEffect,d=a.useDebugValue;function f(e){var t=e.getSnapshot;e=e.value;try{var r=t();return!s(e,r)}catch(n){return!0}}var p="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var r=t(),n=c({inst:{value:r,getSnapshot:t}}),i=n[0].inst,o=n[1];return u((function(){i.value=r,i.getSnapshot=t,f(i)&&o({inst:i})}),[e,r,t]),l((function(){return f(i)&&o({inst:i}),e((function(){f(i)&&o({inst:i})}))}),[e]),d(r),r};o.useSyncExternalStore=void 0!==a.useSyncExternalStore?a.useSyncExternalStore:p,i.exports=o;var h=i.exports;var g=Object.prototype.hasOwnProperty;const m=new WeakMap,v=()=>{},w=v(),b=Object,y=e=>e===w,E=e=>"function"==typeof e,S=(e,t)=>({...e,...t}),L=e=>E(e.then),O={},k={},R="undefined",C=typeof window!=R,x=typeof document!=R,I=C&&"Deno"in window,T=(e,t)=>{const r=m.get(e);return[()=>!y(t)&&e.get(t)||O,n=>{if(!y(t)){const i=e.get(t);t in k||(k[t]=i),r[5](t,S(i,n),i||O)}},r[6],()=>!y(t)&&t in k?k[t]:!y(t)&&e.get(t)||O]};let U=!0;const[P,j]=C&&window.addEventListener?[window.addEventListener.bind(window),window.removeEventListener.bind(window)]:[v,v],A={isOnline:()=>U,isVisible:()=>{const e=x&&document.visibilityState;return y(e)||"hidden"!==e}},_={initFocus:e=>(x&&document.addEventListener("visibilitychange",e),P("focus",e),()=>{x&&document.removeEventListener("visibilitychange",e),j("focus",e)}),initReconnect:e=>{const t=()=>{U=!0,e()},r=()=>{U=!1};return P("online",t),P("offline",r),()=>{j("online",t),j("offline",r)}}},M=!t.useId,$=!C||I,F=e=>C&&typeof window.requestAnimationFrame!=R?window.requestAnimationFrame(e):setTimeout(e,1),V=$?e.useEffect:e.useLayoutEffect,D="undefined"!=typeof navigator&&navigator.connection,W=!$&&D&&(["slow-2g","2g"].includes(D.effectiveType)||D.saveData),N=new WeakMap,z=(e,t)=>b.prototype.toString.call(e)===`[object ${t}]`;let B=0;const G=e=>{const t=typeof e,r=z(e,"Date"),n=z(e,"RegExp"),i=z(e,"Object");let o,a;if(b(e)!==e||r||n)o=r?e.toJSON():"symbol"==t?e.toString():"string"==t?JSON.stringify(e):""+e;else{if(o=N.get(e),o)return o;if(o=++B+"~",N.set(e,o),Array.isArray(e)){for(o="@",a=0;a<e.length;a++)o+=G(e[a])+",";N.set(e,o)}if(i){o="#";const t=b.keys(e).sort();for(;!y(a=t.pop());)y(e[a])||(o+=a+":"+G(e[a])+",");N.set(e,o)}}return o},J=e=>{if(E(e))try{e=e()}catch(r){e=""}const t=e;return[e="string"==typeof e?e:(Array.isArray(e)?e.length:e)?G(e):"",t]};let q=0;const H=()=>++q;async function X(...e){const[t,r,n,i]=e,o=S({populateCache:!0,throwOnError:!0},"boolean"==typeof i?{revalidate:i}:i||{});let a=o.populateCache;const s=o.rollbackOnError;let c=o.optimisticData;const l=o.throwOnError;if(E(r)){const e=r,n=[],i=t.keys();for(const r of i)!/^\$(inf|sub)\$/.test(r)&&e(t.get(r)._k)&&n.push(r);return Promise.all(n.map(u))}return u(r);async function u(r){const[i]=J(r);if(!i)return;const[u,d]=T(t,i),[f,p,h,g]=m.get(t),v=()=>{const e=f[i];return(E(o.revalidate)?o.revalidate(u().data,r):!1!==o.revalidate)&&(delete h[i],delete g[i],e&&e[0])?e[0](2).then((()=>u().data)):u().data};if(e.length<3)return v();let b,S=n;const O=H();p[i]=[O,0];const k=!y(c),R=u(),C=R.data,x=R._c,I=y(x)?C:x;if(k&&(c=E(c)?c(I,C):c,d({data:c,_c:I})),E(S))try{S=S(I)}catch(U){b=U}if(S&&L(S)){if(S=await S.catch((e=>{b=e})),O!==p[i][0]){if(b)throw b;return S}b&&k&&(e=>"function"==typeof s?s(e):!1!==s)(b)&&(a=!0,d({data:I,_c:w}))}if(a&&!b)if(E(a)){const e=a(S,I);d({data:e,error:w,_c:w})}else d({data:S,error:w,_c:w});if(p[i][1]=H(),Promise.resolve(v()).then((()=>{d({_c:w})})),!b)return S;if(l)throw b}}const Y=(e,t)=>{for(const r in e)e[r][0]&&e[r][0](t)},Z=(e,t)=>{if(!m.has(e)){const r=S(_,t),n=Object.create(null),i=X.bind(w,e);let o=v;const a=Object.create(null),s=(e,t)=>{const r=a[e]||[];return a[e]=r,r.push(t),()=>r.splice(r.indexOf(t),1)},c=(t,r,n)=>{e.set(t,r);const i=a[t];if(i)for(const e of i)e(r,n)},l=()=>{if(!m.has(e)&&(m.set(e,[n,Object.create(null),Object.create(null),Object.create(null),i,c,s]),!$)){const t=r.initFocus(setTimeout.bind(w,Y.bind(w,n,0))),i=r.initReconnect(setTimeout.bind(w,Y.bind(w,n,1)));o=()=>{t&&t(),i&&i(),m.delete(e)}}};return l(),[e,i,l,o]}return[e,m.get(e)[4]]},Q=function e(t,r){var n,i;if(t===r)return!0;if(t&&r&&(n=t.constructor)===r.constructor){if(n===Date)return t.getTime()===r.getTime();if(n===RegExp)return t.toString()===r.toString();if(n===Array){if((i=t.length)===r.length)for(;i--&&e(t[i],r[i]););return-1===i}if(!n||"object"==typeof t){for(n in i=0,t){if(g.call(t,n)&&++i&&!g.call(r,n))return!1;if(!(n in r)||!e(t[n],r[n]))return!1}return Object.keys(r).length===i}}return t!=t&&r!=r},[K,ee]=Z(new Map),te=S({onLoadingSlow:v,onSuccess:v,onError:v,onErrorRetry:(e,t,r,n,i)=>{const o=r.errorRetryCount,a=i.retryCount,s=~~((Math.random()+.5)*(1<<(a<8?a:8)))*r.errorRetryInterval;!y(o)&&a>o||setTimeout(n,s,i)},onDiscarded:v,revalidateOnFocus:!0,revalidateOnReconnect:!0,revalidateIfStale:!0,shouldRetryOnError:!0,errorRetryInterval:W?1e4:5e3,focusThrottleInterval:5e3,dedupingInterval:2e3,loadingTimeout:W?5e3:3e3,compare:Q,isPaused:()=>!1,cache:K,mutate:ee,fallback:{}},A),re=(e,t)=>{const r=S(e,t);if(t){const{use:n,fallback:i}=e,{use:o,fallback:a}=t;n&&o&&(r.use=n.concat(o)),i&&a&&(r.fallback=S(i,a))}return r},ne=e.createContext({}),ie=C&&window.__SWR_DEVTOOLS_USE__,oe=ie?window.__SWR_DEVTOOLS_USE__:[],ae=oe.concat((e=>(t,r,n)=>e(t,r&&((...e)=>{const[n]=J(t),[,,,i]=m.get(K);if(n.startsWith("$inf$"))return r(...e);const o=i[n];return y(o)?r(...e):(delete i[n],o)}),n)));ie&&(window.__SWR_DEVTOOLS_REACT__=t);const se=t.use||(e=>{switch(e.status){case"pending":throw e;case"fulfilled":return e.value;case"rejected":throw e.reason;default:throw e.status="pending",e.then((t=>{e.status="fulfilled",e.value=t}),(t=>{e.status="rejected",e.reason=t})),e}}),ce={dedupe:!0},le=b.defineProperty((t=>{const{value:r}=t,n=e.useContext(ne),i=E(r),o=e.useMemo((()=>i?r(n):r),[i,n,r]),a=e.useMemo((()=>i?o:re(n,o)),[i,n,o]),s=o&&o.provider,c=e.useRef(w);s&&!c.current&&(c.current=Z(s(a.cache||K),o));const l=c.current;return l&&(a.cache=l[0],a.mutate=l[1]),V((()=>{if(l)return l[2]&&l[2](),l[3]}),[]),e.createElement(ne.Provider,S(t,{value:a}))}),"defaultValue",{value:te}),ue=(de=(t,r,n)=>{const{cache:i,compare:o,suspense:a,fallbackData:s,revalidateOnMount:c,revalidateIfStale:l,refreshInterval:u,refreshWhenHidden:d,refreshWhenOffline:f,keepPreviousData:p}=n,[g,v,b,O]=m.get(i),[k,R]=J(t),C=e.useRef(!1),x=e.useRef(!1),I=e.useRef(k),U=e.useRef(r),P=e.useRef(n),j=()=>P.current,A=()=>j().isVisible()&&j().isOnline(),[_,D,W,N]=T(i,k),z=e.useRef({}).current,B=y(s)?y(n.fallback)?w:n.fallback[k]:s,G=(e,t)=>{for(const r in z){const n=r;if("data"===n){if(!o(e[n],t[n])){if(!y(e[n]))return!1;if(!o(ne,t[n]))return!1}}else if(t[n]!==e[n])return!1}return!0},q=e.useMemo((()=>{const e=!!k&&!!r&&(y(c)?!j().isPaused()&&!a&&!1!==l:c),t=t=>{const r=S(t);return delete r._k,e?{isValidating:!0,isLoading:!0,...r}:r},n=_(),i=N(),o=t(n),s=n===i?o:t(i);let u=o;return[()=>{const e=t(_());return G(e,u)?(u.data=e.data,u.isLoading=e.isLoading,u.isValidating=e.isValidating,u.error=e.error,u):(u=e,e)},()=>s]}),[i,k]),Y=h.useSyncExternalStore(e.useCallback((e=>W(k,((t,r)=>{G(r,t)||e()}))),[i,k]),q[0],q[1]),Z=!C.current,Q=g[k]&&g[k].length>0,K=Y.data,ee=y(K)?B&&L(B)?se(B):B:K,te=Y.error,re=e.useRef(ee),ne=p?y(K)?y(re.current)?ee:re.current:K:ee,ie=!(Q&&!y(te))&&(Z&&!y(c)?c:!j().isPaused()&&(a?!y(ee)&&l:y(ee)||l)),oe=!!(k&&r&&Z&&ie),ae=y(Y.isValidating)?oe:Y.isValidating,le=y(Y.isLoading)?oe:Y.isLoading,ue=e.useCallback((async e=>{const t=U.current;if(!k||!t||x.current||j().isPaused())return!1;let r,i,a=!0;const s=e||{},c=!b[k]||!s.dedupe,l=()=>M?!x.current&&k===I.current&&C.current:k===I.current,u={isValidating:!1,isLoading:!1},d=()=>{D(u)},f=()=>{const e=b[k];e&&e[1]===i&&delete b[k]},p={isValidating:!0};y(_().data)&&(p.isLoading=!0);try{if(c&&(D(p),n.loadingTimeout&&y(_().data)&&setTimeout((()=>{a&&l()&&j().onLoadingSlow(k,n)}),n.loadingTimeout),b[k]=[t(R),H()]),[r,i]=b[k],r=await r,c&&setTimeout(f,n.dedupingInterval),!b[k]||b[k][1]!==i)return c&&l()&&j().onDiscarded(k),!1;u.error=w;const e=v[k];if(!y(e)&&(i<=e[0]||i<=e[1]||0===e[1]))return d(),c&&l()&&j().onDiscarded(k),!1;const s=_().data;u.data=o(s,r)?s:r,c&&l()&&j().onSuccess(r,k,n)}catch(h){f();const e=j(),{shouldRetryOnError:t}=e;e.isPaused()||(u.error=h,c&&l()&&(e.onError(h,k,e),(!0===t||E(t)&&t(h))&&(j().revalidateOnFocus&&j().revalidateOnReconnect&&!A()||e.onErrorRetry(h,k,e,(e=>{const t=g[k];t&&t[0]&&t[0](3,e)}),{retryCount:(s.retryCount||0)+1,dedupe:!0}))))}return a=!1,d(),!0}),[k,i]),de=e.useCallback(((...e)=>X(i,I.current,...e)),[]);if(V((()=>{U.current=r,P.current=n,y(K)||(re.current=K)})),V((()=>{if(!k)return;const e=ue.bind(w,ce);let t=0;if(j().revalidateOnFocus){const e=Date.now();t=e+j().focusThrottleInterval}const r=((e,t,r)=>{const n=t[e]||(t[e]=[]);return n.push(r),()=>{const e=n.indexOf(r);e>=0&&(n[e]=n[n.length-1],n.pop())}})(k,g,((r,n={})=>{if(0==r){const r=Date.now();j().revalidateOnFocus&&r>t&&A()&&(t=r+j().focusThrottleInterval,e())}else if(1==r)j().revalidateOnReconnect&&A()&&e();else{if(2==r)return ue();if(3==r)return ue(n)}}));return x.current=!1,I.current=k,C.current=!0,D({_k:R}),ie&&(y(ee)||$?e():F(e)),()=>{x.current=!0,r()}}),[k]),V((()=>{let e;function t(){const t=E(u)?u(_().data):u;t&&-1!==e&&(e=setTimeout(r,t))}function r(){_().error||!d&&!j().isVisible()||!f&&!j().isOnline()?t():ue(ce).then(t)}return t(),()=>{e&&(clearTimeout(e),e=-1)}}),[u,d,f,k]),e.useDebugValue(ne),a&&y(ee)&&k){if(!M&&$)throw new Error("Fallback data is required when using Suspense in SSR.");U.current=r,P.current=n,x.current=!1;const e=O[k];if(!y(e)){const t=de(e);se(t)}if(!y(te))throw te;{const e=ue(ce);y(ne)||(e.status="fulfilled",e.value=!0),se(e)}}return{mutate:de,get data(){return z.data=!0,ne},get error(){return z.error=!0,te},get isValidating(){return z.isValidating=!0,ae},get isLoading(){return z.isLoading=!0,le}}},function(...t){const r=S(te,e.useContext(ne)),[n,i,o]=(e=>E(e[1])?[e[0],e[1],e[2]||{}]:[e[0],null,(null===e[1]?e[2]:e[1])||{}])(t),a=re(r,o);let s=de;const{use:c}=a,l=(c||[]).concat(ae);for(let e=l.length;e--;)s=l[e](s);return s(n,i||a.fetcher||null,a)});var de;let fe=null;const pe=fe||(fe=n("https://vsnsglgyapexhwyfylic.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZzbnNnbGd5YXBleGh3eWZ5bGljIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUyNjExOTEsImV4cCI6MjA2MDgzNzE5MX0.6CQWpMT14h2kaIOk1_LMECuJrfRdmiGRo3vGyEDW9tM",{auth:{persistSession:!0,autoRefreshToken:!0,detectSessionInUrl:!0},global:{headers:{"X-Client-Info":"supabase-js/2.x"}},fetch:(e,t)=>{const r=new AbortController,n=setTimeout((()=>{r.abort()}),15e3),i={...t,signal:r.signal,headers:{...t?.headers,"Cache-Control":"no-cache"}};return fetch(e,i).finally((()=>{clearTimeout(n)}))}}),fe),he=e=>e.select("\n    id,\n    title,\n    description,\n    thumbnail_url,\n    video_url,\n    duration,\n    views,\n    likes,\n    is_hd,\n    user_id,\n    created_at,\n    updated_at,\n    category,\n    is_part_of_multi_upload,\n    is_first_in_multi_upload,\n    total_parts_in_multi_upload,\n    tags,\n    creator:profiles(id, username, avatar_url)\n  "),ge=e=>{if(!e)return"";const t=e.trim().replace(/[\r\n\t]/g,"").replace(/%0A/g,"");if(t.includes("supabase.co/storage/v1/object/public/")||t.includes("bluefilmx.com/media/")||t.startsWith("http://")||t.startsWith("https://"))return t;if(t.startsWith("/storage/")||t.startsWith("storage/")){return`https://vsnsglgyapexhwyfylic.supabase.co/${t.startsWith("/")?t.slice(1):t}`}if(t.startsWith("/media/")||t.startsWith("media/")){return`http://www.bluefilmx.com${t.startsWith("/")?t:`/${t}`}`}return t},me=e=>{const t=ge(e);return t||we()},ve=e=>{const t=ge(e);return t||""},we=()=>"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='640' height='360' viewBox='0 0 640 360'%3E%3Crect width='640' height='360' fill='%23374151'/%3E%3Cg transform='translate(320,180)'%3E%3Ccircle cx='0' cy='0' r='30' fill='%236B7280'/%3E%3Cpolygon points='-10,-15 -10,15 20,0' fill='%23F3F4F6'/%3E%3C/g%3E%3Ctext x='320' y='320' font-family='Arial, sans-serif' font-size='18' fill='%239CA3AF' text-anchor='middle'%3ENo Thumbnail%3C/text%3E%3C/svg%3E",be=async(e,t)=>{const{priority:r="high",maxPreload:n=6,includeNextPage:i=!1}=t||{},o=e.slice(0,n).map((async(e,t)=>{if(e.thumbnailUrl){const n=document.createElement("link");n.rel="preload",n.as="image";const i=t<3?640:320;n.href=Ee(me(e.thumbnailUrl),i),"high"===r&&t<3?n.setAttribute("fetchpriority","high"):n.setAttribute("fetchpriority","low"),document.head.appendChild(n)}}));await Promise.allSettled(o)},ye=e=>{if(!e)return"";const t=e.replace(/([^:]\/)\/+/g,"$1");return t.startsWith("http://")&&"https:"===window.location.protocol?t.replace("http://","https://"):t},Ee=(e,t,r)=>{const n=ge(e);if(!n||!t)return n;if(n.includes("supabase.co/storage/v1/object/public/"))try{const e=new URL(n);e.searchParams.set("width",t.toString());const i=t<=320?"75":t<=640?"80":"85";return e.searchParams.set("quality",i),r&&"auto"!==r&&e.searchParams.set("format",r),e.toString()}catch(i){return n}return n},Se=()=>{const e=document.createElement("canvas");e.width=1,e.height=1;try{if(0===e.toDataURL("image/avif").indexOf("data:image/avif"))return"avif";if(0===e.toDataURL("image/webp").indexOf("data:image/webp"))return"webp"}catch(t){}return"auto"},Le=e=>Ee(e,40,"auto"),Oe=r(((e,t)=>({user:null,profile:null,isLoading:!0,isApproved:!1,signUp:async(t,r)=>{const{data:n,error:i}=await pe.auth.signUp({email:t,password:r});if(i)throw i;if(n.user){const r=t.split("@")[0],{error:i}=await pe.from("profiles").insert([{id:n.user.id,username:r,is_approved:!1}]);if(i)throw i;const{data:o}=await pe.from("profiles").select("*").eq("id",n.user.id).single();e({user:n.user,profile:o,isApproved:o?.is_approved||!1})}},signIn:async(t,r)=>{const{data:n,error:i}=await pe.auth.signInWithPassword({email:t,password:r});if(i)throw i;if(n.user){const{data:r,error:i}=await pe.from("profiles").select("*").eq("id",n.user.id).single();if(i&&"PGRST116"===i.code){const r=t.split("@")[0],{data:i,error:o}=await pe.from("profiles").insert([{id:n.user.id,username:r,is_approved:!1}]).select().single(),{data:a,error:s}=await pe.from("profiles").select("*").eq("id",n.user.id).single(),c=a||i;e({user:n.user,profile:c,isApproved:c?.is_approved||!1,isLoading:!1})}else e(i?{user:n.user,profile:null,isApproved:!1,isLoading:!1}:{user:n.user,profile:r,isApproved:r?.is_approved||!1,isLoading:!1})}},signOut:async()=>{try{e({user:null,profile:null,isApproved:!1,isLoading:!1});const{error:t}=await pe.auth.signOut();if(t)throw t}catch(t){throw e({user:null,profile:null,isApproved:!1,isLoading:!1}),t}},loadUser:async()=>{try{const t=new Promise(((e,t)=>{setTimeout((()=>t(new Error("loadUser timeout after 10 seconds"))),1e4)})),r=await Promise.race([pe.auth.getSession(),t]),{data:{session:n}}=r;if(!n)return void e({user:null,profile:null,isLoading:!1,isApproved:!1});const i=await Promise.race([pe.auth.getUser(),t]),{data:{user:o}}=i;if(o){const r=await Promise.race([pe.from("profiles").select("*").eq("id",o.id).single(),t]),{data:n,error:i}=r;if(i&&"PGRST116"===i.code){const t=o.email?o.email.split("@")[0]:`user_${Date.now()}`,{data:r,error:n}=await pe.from("profiles").insert([{id:o.id,username:t,is_approved:!1}]).select().single(),{data:i,error:a}=await pe.from("profiles").select("*").eq("id",o.id).single(),s=i||r;e({user:o,profile:s,isLoading:!1,isApproved:s?.is_approved||!1})}else e(i?{user:o,profile:null,isLoading:!1,isApproved:!1}:{user:o,profile:n,isLoading:!1,isApproved:n?.is_approved||!1})}else e({user:null,profile:null,isLoading:!1,isApproved:!1})}catch(t){e({user:null,profile:null,isLoading:!1,isApproved:!1})}}})));pe.auth.onAuthStateChange((async(e,t)=>{const{loadUser:r}=Oe.getState(),n=/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);if("SIGNED_OUT"!==e&&t){if("SIGNED_IN"===e||"TOKEN_REFRESHED"===e)try{n&&await new Promise((e=>setTimeout(e,200))),await r()}catch(i){n||Oe.setState({user:null,profile:null,isApproved:!1,isLoading:!1})}}else{const e=()=>{Oe.setState({user:null,profile:null,isApproved:!1,isLoading:!1})};n?setTimeout(e,100):e()}}));const ke=(e="default")=>{const t=e.split("").reduce(((e,t)=>e+t.charCodeAt(0)),0),r=(e,t)=>e[t%e.length],n=[`mouth=${r(["smile","tongue","twinkle","vomit"],t)}`,`eyes=${r(["happy","hearts","stars","wink","winkWacky"],t+1)}`,`top=${r(["longHair","shortHair","eyepatch","hat","hijab","turban","bigHair","bob","bun"],t+2)}`,`accessories=${r(["kurt","prescription01","prescription02","round","sunglasses","wayfarers"],t+3)}`,`hairColor=${r(["auburn","black","blonde","brown","pastel","platinum","red","blue","pink"],t+4)}`,`facialHair=${r(["medium","light","majestic","fancy","magnum"],t+5)}`,`clothes=${r(["blazer","sweater","hoodie","overall","shirtCrewNeck"],t+6)}`,`fabric=${r(["denim","graphicShirt","stripes","dots"],t+7)}`,`backgroundColor=${r(["b6e3f4","c0aede","ffd5dc","ffdfbf","d1d4f9","c0e8d5"],t+8)}`].join("&");return`https://avatars.dicebear.com/api/avataaars/${encodeURIComponent(e)}.svg?${n}`},Re=()=>`https://avatars.dicebear.com/api/bottts/fallback.svg?${["backgroundColor=b6e3f4","colors=blue","mouthChance=100","sidesChance=100","topChance=100"].join("&")}`,Ce=()=>{const e=[],t=[];try{const n=localStorage.getItem("sb-lfnxllcoixgfkasdjtnj-auth-token");if(n)try{const r=JSON.parse(n);r.expires_at&&new Date(1e3*r.expires_at)<new Date&&(e.push("Expired authentication token found"),t.push("Clear authentication data and re-login"))}catch(r){e.push("Corrupted authentication data"),t.push("Clear authentication storage")}const i=localStorage.getItem("app-version"),o="1.0.0";i&&i!==o&&(e.push(`Version mismatch: stored ${i}, current ${o}`),t.push("Clear cached data for new version"));let a=0;for(let e in localStorage)localStorage.hasOwnProperty(e)&&(a+=localStorage[e].length);a>5242880&&(e.push("Large localStorage usage detected"),t.push("Consider clearing non-essential cached data"));const s=localStorage.getItem("user-preferences-storage");if(s)try{const r=JSON.parse(s);r.state&&Object.keys(r.state.watchHistory||{}).length>1e3&&(e.push("Large watch history detected"),t.push("Consider clearing old watch history"))}catch(r){e.push("Corrupted user preferences data"),t.push("Reset user preferences")}}catch(n){e.push("Error checking browser state"),t.push("Clear all browser data")}return{hasConflicts:e.length>0,conflicts:e,recommendations:t}},xe=new Map,Ie=()=>({size:xe.size,keys:Array.from(xe.keys())}),Te=()=>({version:"1.0.0",timestamp:Date.now(),buildId:"dev"}),Ue=async()=>{try{if("caches"in window){const e=await caches.keys();await Promise.all(e.map((e=>caches.delete(e))))}["user-preferences-storage","disclaimerAccepted","recentSearches"].forEach((e=>{try{localStorage.removeItem(e)}catch(t){}})),(()=>{try{const e=Te();localStorage.setItem("app-version",e.version),localStorage.setItem("app-build-id",e.buildId),localStorage.setItem("version-updated",e.timestamp.toString())}catch(e){}})()}catch(e){}},Pe=async()=>{try{return!(()=>{try{return localStorage.getItem("app-version")===Te().version}catch(e){return!1}})()&&(await Ue(),!0)}catch(e){return!1}},je=e=>{let t;return t=window.setInterval((async()=>{try{const t=await fetch("/version.json?"+Date.now(),{cache:"no-cache"});if(t.ok){const r=await t.json(),n=Te().version;r.version!==n&&e()}}catch(t){}}),3e5),()=>{t&&clearInterval(t)}},Ae=()=>{window.addEventListener("unhandledrejection",(e=>{const t=e.reason;if(t&&"object"==typeof t&&"message"in t){const r=String(t.message);if(r.includes("Cache")||r.includes("service-worker")||r.includes("Failed to execute 'put' on 'Cache'"))return void e.preventDefault()}})),window.addEventListener("error",(e=>{const t=e.error;t&&t.message&&(t.message.includes("Cache")||t.message.includes("service-worker")||t.message.includes("Failed to execute 'put' on 'Cache'"))&&e.preventDefault()}))},_e=e=>e>=1e6?`${(e/1e6).toFixed(1)}M`:e>=1e3?`${(e/1e3).toFixed(1)}k`:e.toString(),Me=e=>{const t=Math.floor(e/3600),r=Math.floor(e%3600/60),n=Math.floor(e%60);return t>0?`${t}:${r.toString().padStart(2,"0")}:${n.toString().padStart(2,"0")}`:`${r}:${n.toString().padStart(2,"0")}`},$e=(e=640,t=360,r="No Image")=>`data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='${e}' height='${t}' viewBox='0 0 ${e} ${t}'%3E%3Crect width='${e}' height='${t}' fill='%23333'/%3E%3Ctext x='50%25' y='50%25' font-family='Arial, sans-serif' font-size='24' fill='%23fff' text-anchor='middle' dominant-baseline='middle'%3E${r}%3C/text%3E%3C/svg%3E`,Fe=()=>$e(640,360,"No Thumbnail"),Ve=[/^(.*?)(?:\s*[-:]\s*(?:Part|Episode|Ep\.?|#|S\d+E)\s*(\d+))(?:\s*[-:]\s*(.*))?$/i,/^(.*?)(?:\s*\((?:Part|Episode|Ep\.?|#|S\d+E)\s*(\d+)\))(?:\s*[-:]\s*(.*))?$/i,/^(.*?)\s+(\d+)$/i,/^(\d+)(?:\.|\s*-\s*)\s*(.*)$/i,/^(.*?)(?::\s*)(.*)(?:\s+)(\d+)(?:\s*[-:]\s*(.*))?$/i,/^(.*?)(?:\s*[\[\{]\s*(?:Part|Episode|Ep\.?|#)?\s*(\d+)\s*[\]\}])(?:\s*[-:]\s*(.*))?$/i,/^(.*?)(?:\s+(?:Season|S)\s*(\d+)\s*(?:Episode|Ep\.?|E)\s*(\d+))(?:\s*[-:]\s*(.*))?$/i,/^(.*?)(?:\s+S(\d+)E(\d+))(?:\s*[-:]\s*(.*))?$/i];function De(e){const t=e.replace(/\s+/g," ").trim();for(let n=0;n<Ve.length;n++){const e=Ve[n],r=t.match(e);if(r){if(0===n||1===n||5===n){return{baseTitle:r[1].trim(),partNumber:parseInt(r[2],10)}}if(2===n){return{baseTitle:r[1].trim(),partNumber:parseInt(r[2],10)}}if(3===n){return{baseTitle:r[2].trim(),partNumber:parseInt(r[1],10)}}if(4===n){return{baseTitle:r[1].trim(),partNumber:parseInt(r[3],10)}}if(6===n||7===n){const e=r[1].trim(),t=parseInt(r[2],10),n=parseInt(r[3]||"0",10);return{baseTitle:e,partNumber:n>0?n:t}}}}const r=t.match(/^(\d+)$/);return r?{baseTitle:"Numbered Series",partNumber:parseInt(r[1],10)}:null}function We(e){let t=e.toLowerCase();return["the","a","an","and","or","but","in","on","at","to","for","with","by","of"].forEach((e=>{t=t.replace(new RegExp(`\\b${e}\\b`,"g"),"")})),t=t.replace(/[^\w\s]/g,"").replace(/\s+/g," ").trim(),t}function Ne(e,t){const r=De(e),n=De(t);if(!r||!n)return!1;const i=We(r.baseTitle),o=We(n.baseTitle);return i===o||ze(i,o,.7)}function ze(e,t,r){if(e.length<5||t.length<5){return 1-Be(e,t)/Math.max(e.length,t.length)>=.9}if(e.includes(t)||t.includes(e))return!0;return 1-Be(e,t)/Math.max(e.length,t.length)>=r}function Be(e,t){const r=e.length,n=t.length,i=Array(r+1).fill(null).map((()=>Array(n+1).fill(0)));for(let o=0;o<=r;o++)i[o][0]=o;for(let o=0;o<=n;o++)i[0][o]=o;for(let o=1;o<=r;o++)for(let r=1;r<=n;r++){const n=e[o-1]===t[r-1]?0:1;i[o][r]=Math.min(i[o-1][r]+1,i[o][r-1]+1,i[o-1][r-1]+n)}return i[r][n]}function Ge(e){if(!e||0===e.length)return[];const t=new Map,r=[],n=[],i=[];for(const s of e){De(s.title)?n.push(s):i.push(s)}for(const s of n){const e=De(s.title);let r=!1;for(const[n,i]of t.entries())if(ze(We(e.baseTitle),We(n),.7)){i.push(s),r=!0;break}r||t.set(e.baseTitle,[s])}for(const s of i){let e=!1;for(const[r,n]of t.entries()){const t=We(s.title),i=We(r);if(t.includes(i)||i.includes(t)||ze(t,i,.6)){n.push(s),e=!0;break}}e||r.push(s)}const o=new Map;for(const s of r){const e=We(s.title);o.has(e)?o.get(e).push(s):o.set(e,[s])}const a=[];for(const[s,c]of t.entries())c.length>1?(c.sort(((e,t)=>{const r=De(e.title),n=De(t.title);return r&&n?r.partNumber-n.partNumber:r?-1:n?1:e.title.localeCompare(t.title)})),a.push({baseTitle:s,videos:c,totalVideos:c.length})):a.push(c[0]);for(const[s,c]of o.entries())c.length>1?a.push({baseTitle:c[0].title,videos:c,totalVideos:c.length}):a.push(c[0]);return a}const Je=()=>{try{if("undefined"!=typeof sessionStorage){const e=sessionStorage.getItem("isMobileDevice");if(null!==e)return"true"===e}}catch(e){}return(()=>{if("undefined"==typeof window)return!1;const t=navigator.userAgent||navigator.vendor||window.opera,r="ontouchstart"in window||navigator.maxTouchPoints>0||navigator.msMaxTouchPoints>0,n=window.innerWidth<=768,i=/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(t)||r&&n;try{"undefined"!=typeof sessionStorage&&sessionStorage.setItem("isMobileDevice",String(i))}catch(e){}return i})()},qe=()=>/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent),He=()=>{const e=navigator.onLine,t=navigator.connection||navigator.mozConnection||navigator.webkitConnection,r=t?.effectiveType||"unknown";return{isOnline:e,connectionType:r,isSlowConnection:"slow-2g"===r||"2g"===r}},Xe=e=>{const t=(()=>{const e=qe(),t=navigator.connection||navigator.mozConnection||navigator.webkitConnection,r=t&&("slow-2g"===t.effectiveType||"2g"===t.effectiveType);return{chunkSize:e?r?524288:1048576:2097152,maxRetries:e?5:3,timeout:e?6e4:3e4,maxConcurrentChunks:e?1:3,compressionQuality:e?.7:.8,maxFileSize:e?62914560:104857600}})(),r=He();if(!r.isOnline)return{isValid:!1,error:"No internet connection. Please check your network and try again."};if(e.size>t.maxFileSize){return{isValid:!1,error:`File is too large. Maximum size is ${Math.round(t.maxFileSize/1048576)}MB. Your file is ${Math.round(e.size/1048576)}MB.`}}return r.isSlowConnection&&e.size>10485760?{isValid:!1,error:"File is too large for your current connection speed. Please try with a smaller file or better connection."}:{isValid:!0}},Ye=async(e,t=0)=>{try{(()=>{const{user:e}=Oe.getState();if(!e)throw new Error("Authentication required for thumbnail generation")})()}catch(r){throw r}if(!(()=>{if(!/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent))return!0;const e=navigator.deviceMemory;return!(e&&e<2)})())throw new Error("Insufficient device memory for thumbnail generation. Please try uploading without a custom thumbnail.");return new Promise(((n,i)=>{const o=/Android/i.test(navigator.userAgent),a=document.createElement("video");let s;a.preload="metadata",a.muted=!0,a.playsInline=!0,a.crossOrigin="anonymous",o&&(a.controls=!1,a.autoplay=!1,a.style.maxWidth="320px",a.style.maxHeight="240px");try{s=URL.createObjectURL(e),a.src=s}catch(r){return void i(new Error("Failed to create video URL: "+r))}const c=setTimeout((()=>{URL.revokeObjectURL(s),i(new Error("Thumbnail generation timed out"))}),o?15e3:1e4);a.onloadedmetadata=()=>{try{const{user:e}=Oe.getState();if(!e)return clearTimeout(c),URL.revokeObjectURL(s),void i(new Error("Authentication lost during thumbnail generation"));t>a.duration&&(t=a.duration/2),o?setTimeout((()=>{try{const{user:e}=Oe.getState();if(!e)return clearTimeout(c),URL.revokeObjectURL(s),void i(new Error("Authentication lost during thumbnail generation"));a.currentTime=t}catch(e){clearTimeout(c),URL.revokeObjectURL(s),i(new Error("Failed to seek video on Android: "+e))}}),100):a.currentTime=t}catch(r){clearTimeout(c),URL.revokeObjectURL(s),i(new Error("Failed to seek video: "+r))}},a.onseeked=()=>{try{clearTimeout(c);const{user:e}=Oe.getState();if(!e)return URL.revokeObjectURL(s),void i(new Error("Authentication lost during thumbnail generation"));const t=()=>{try{const{user:e}=Oe.getState();if(!e)return URL.revokeObjectURL(s),void i(new Error("Authentication lost during thumbnail generation"));const t=document.createElement("canvas"),r=a.videoWidth||640,c=a.videoHeight||480;if(o){const e=800,n=r/c;r>c?(t.width=Math.min(r,e),t.height=t.width/n):(t.height=Math.min(c,e),t.width=t.height*n)}else t.width=r,t.height=c;const l=t.getContext("2d");if(!l)return URL.revokeObjectURL(s),void i(new Error("Failed to get canvas context"));l.drawImage(a,0,0,t.width,t.height);const u=o?.7:.8;t.toBlob((e=>{e?(URL.revokeObjectURL(s),n(e)):(URL.revokeObjectURL(s),i(new Error("Failed to generate thumbnail")))}),"image/jpeg",u)}catch(r){clearTimeout(c),URL.revokeObjectURL(s),i(new Error("Error during thumbnail generation: "+r))}};o?setTimeout(t,100):t()}catch(r){clearTimeout(c),URL.revokeObjectURL(s),i(new Error("Error during thumbnail generation: "+r))}},a.onerror=e=>{clearTimeout(c),URL.revokeObjectURL(s),i(new Error("Error loading video: "+e))};try{a.load()}catch(r){clearTimeout(c),URL.revokeObjectURL(s),i(new Error("Failed to load video: "+r))}}))},Ze=async(e,t=3)=>new Promise(((r,n)=>{const i=document.createElement("video");i.preload="metadata",i.muted=!0;const o=URL.createObjectURL(e);i.src=o,i.onloadedmetadata=async()=>{try{const n=[],a=i.duration;for(let r=0;r<t;r++){const i=a*(r+1)/(t+1),o=await Ye(e,i);n.push(o)}URL.revokeObjectURL(o),r(n)}catch(a){URL.revokeObjectURL(o),n(a)}},i.onerror=()=>{URL.revokeObjectURL(o),n(new Error("Error loading video"))},i.load()})),Qe=()=>/Android/i.test(navigator.userAgent),Ke=e=>{const t=Qe(),r=t?62914560:52428800;if(e.size>r)return{isValid:!1,error:`File is too large. Maximum size is ${r/1048576}MB. Your file is ${(e.size/1048576).toFixed(2)}MB.`};const n=["video/mp4","video/webm","video/quicktime","video/x-msvideo","video/3gpp","video/x-ms-wmv","video/avi","video/mov","video/x-flv","video/x-matroska","video/3gp","video/mp2t","video/x-m4v"].includes(e.type)||e.type.startsWith("video/");if(!n&&t){const t=[".mp4",".webm",".mov",".avi",".3gp",".mkv",".flv",".wmv"],r=e.name.toLowerCase();if(!t.some((e=>r.endsWith(e))))return{isValid:!1,error:"Unsupported video format. Please use MP4, WebM, MOV, AVI, or 3GP."}}else if(!n)return{isValid:!1,error:"Unsupported video format. Please use MP4, WebM, MOV, or AVI."};return{isValid:!0}},et=e=>{const t=Qe();if(e.size>5242880)return{isValid:!1,error:`Thumbnail is too large. Maximum size is 5MB. Your file is ${(e.size/1048576).toFixed(2)}MB.`};const r=["image/jpeg","image/jpg","image/png","image/webp","image/gif"].includes(e.type)||e.type.startsWith("image/");if(!r&&t){const t=[".jpg",".jpeg",".png",".webp",".gif"],r=e.name.toLowerCase();if(!t.some((e=>r.endsWith(e))))return{isValid:!1,error:"Unsupported image format. Please use JPG, PNG, WebP, or GIF."}}else if(!r)return{isValid:!1,error:"Unsupported image format. Please use JPG, PNG, WebP, or GIF."};return{isValid:!0}},tt=()=>{const e=[];window.File&&window.FileReader&&window.FileList&&window.Blob||e.push("File API");const t=document.createElement("canvas");t.getContext&&t.getContext("2d")||e.push("Canvas 2D");return document.createElement("video").canPlayType||e.push("Video element"),window.URL&&window.URL.createObjectURL||e.push("Object URLs"),{isSupported:0===e.length,missingFeatures:e}},rt=async(e,t)=>new Promise(((r,n)=>{const{user:i}=Oe.getState();if(!i)return void n(new Error("User not authenticated"));let o=!1;const a=((e,t=1e3)=>{let r,n=!1;const i=()=>{r&&clearInterval(r),n=!1};return n||(n=!0,r=setInterval((()=>{const{user:t}=Oe.getState();t||(i(),e())}),t)),i})((()=>{o||(o=!0,t?.(),n(new Error("Authentication lost during operation")))}));e().then((e=>{o||(o=!0,a(),r(e))})).catch((e=>{o||(o=!0,a(),n(e))}))})),nt=async e=>new Promise(((t,r)=>{const{user:n}=Oe.getState();n?setTimeout((()=>{const{user:e}=Oe.getState();e?t():r(new Error("Authentication lost during delay"))}),e):r(new Error("User not authenticated"))})),it=()=>{try{const{user:e,profile:t}=Oe.getState();if(e&&t){const r={userId:e.id,userEmail:e.email,profileId:t.id,timestamp:Date.now()};localStorage.setItem("auth_backup",JSON.stringify(r))}}catch(e){}};export{qe as A,tt as B,Qe as C,it as D,Ke as E,et as F,nt as G,rt as H,Ze as I,Je as J,ue as K,le as S,Ee as a,Le as b,$e as c,Fe as d,Me as e,ye as f,Se as g,_e as h,he as i,me as j,ve as k,ke as l,Re as m,Ie as n,Pe as o,Ce as p,je as q,Ae as r,pe as s,De as t,Oe as u,Ne as v,Ge as w,be as x,Xe as y,He as z};
