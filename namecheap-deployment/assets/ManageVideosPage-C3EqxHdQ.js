import{r as e,j as s}from"./react-core-B9nwsbCA.js";import{I as t,B as a,P as r,D as l}from"./ui-components-Car30pze.js";import{u as i}from"./main-CGwzGCzN.js";import{h as d,u as n,e as c}from"./utils-DghTBY4T.js";import{X as o,d as x,r as m,E as u,s as g,t as h,T as b,u as y,v as p,j,w as f,G as v,x as N,y as w,S as C,z as S,D as k,R as z,P as D,J as V}from"./icons-BWE0bDFO.js";import{u as P}from"./router-BDggJ1ol.js";import"./supabase-C-51sAsE.js";import"./state-4gBW_4Nx.js";const T=[{id:"1",name:"Amateur",slug:"amateur"},{id:"2",name:"Professional",slug:"professional"},{id:"3",name:"Cosplay",slug:"cosplay"},{id:"4",name:"Couples",slug:"couples"},{id:"5",name:"Solo",slug:"solo"},{id:"6",name:"Verified",slug:"verified"},{id:"7",name:"HD",slug:"hd"},{id:"8",name:"Trending",slug:"trending"}],O=({isOpen:r,onClose:l,video:d})=>{const[n,c]=e.useState(d.title),[u,g]=e.useState(d.description),[h,b]=e.useState(d.category),[y,p]=e.useState({}),[j,f]=e.useState(!1),[v,N]=e.useState(null),[w,C]=e.useState(!1),{updateVideo:S}=i();e.useEffect((()=>{d&&(c(d.title),g(d.description),b(d.category),p({}),N(null),C(!1))}),[d]);return r?s.jsx("div",{className:"fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/70",children:s.jsxs("div",{className:"bg-gray-800 rounded-lg w-full max-w-2xl max-h-[90vh] overflow-y-auto",children:[s.jsxs("div",{className:"flex items-center justify-between p-4 border-b border-gray-700",children:[s.jsx("h2",{className:"text-xl font-bold text-white",children:"Edit Video"}),s.jsx("button",{className:"p-1 rounded-full hover:bg-gray-700 text-gray-400 hover:text-white transition-colors",onClick:l,children:s.jsx(o,{size:20})})]}),s.jsxs("form",{onSubmit:async e=>{if(e.preventDefault(),(()=>{const e={};return n.trim()||(e.title="Title is required"),p(e),0===Object.keys(e).length})()){f(!0),N(null),C(!1);try{await S(d.id,{title:n,description:u,category:h})?(C(!0),setTimeout((()=>{l()}),1500)):N("Failed to update video. Please try again.")}catch(s){N("An unexpected error occurred. Please try again.")}finally{f(!1)}}},className:"p-4",children:[s.jsxs("div",{className:"space-y-4",children:[s.jsx(t,{label:"Title",placeholder:"Enter video title",value:n,onChange:e=>c(e.target.value),error:y.title,fullWidth:!0}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-200 mb-1",children:"Description"}),s.jsx("textarea",{className:"w-full rounded-md bg-gray-700 border border-gray-600 text-white focus:border-orange-500 focus:ring-2 focus:ring-orange-500 focus:ring-opacity-20 p-3 transition-colors placeholder:text-gray-400 min-h-[120px] "+(y.description?"border-red-500":""),placeholder:"Describe your video",value:u,onChange:e=>g(e.target.value)}),y.description&&s.jsx("p",{className:"text-sm text-red-500 mt-1",children:y.description})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-200 mb-1",children:"Category"}),s.jsxs("select",{className:"w-full rounded-md bg-gray-700 border border-gray-600 text-white focus:border-orange-500 focus:ring-2 focus:ring-orange-500 focus:ring-opacity-20 p-3 transition-colors "+(y.category?"border-red-500":""),value:h,onChange:e=>b(e.target.value),children:[s.jsx("option",{value:"",children:"Select a category"}),T.map((e=>s.jsx("option",{value:e.slug,children:e.name},e.id)))]}),y.category&&s.jsx("p",{className:"text-sm text-red-500 mt-1",children:y.category})]}),s.jsx("div",{className:"border border-gray-700 rounded-lg overflow-hidden",children:s.jsx("div",{className:"aspect-video bg-gray-900 relative",children:s.jsx("img",{src:d.thumbnailUrl||"https://placehold.co/640x360/gray/white?text=No+Thumbnail",alt:d.title,className:"w-full h-full object-cover",onError:e=>{e.currentTarget.src="https://placehold.co/640x360/gray/white?text=No+Thumbnail"}})})}),v&&s.jsxs("div",{className:"bg-red-500/20 border border-red-500 rounded-md p-3 flex items-start",children:[s.jsx(x,{size:20,className:"text-red-500 mr-2 flex-shrink-0 mt-0.5"}),s.jsx("p",{className:"text-red-200",children:v})]}),w&&s.jsxs("div",{className:"bg-green-500/20 border border-green-500 rounded-md p-3 flex items-start",children:[s.jsx(x,{size:20,className:"text-green-500 mr-2 flex-shrink-0 mt-0.5"}),s.jsx("p",{className:"text-green-200",children:"Video updated successfully!"})]})]}),s.jsxs("div",{className:"flex justify-end space-x-3 mt-6",children:[s.jsx(a,{variant:"ghost",type:"button",onClick:l,disabled:j,children:"Cancel"}),s.jsx(a,{variant:"primary",type:"submit",leftIcon:s.jsx(m,{size:18}),isLoading:j,disabled:j,children:"Save Changes"})]})]})]})}):null},B=({isOpen:t,onClose:r,video:l})=>{const[d,n]=e.useState(l.status||"public"),[c,b]=e.useState(""),[y,p]=e.useState(""),[j,f]=e.useState(!1),[v,N]=e.useState(null),[w,C]=e.useState(!1),{updateVideoStatus:S}=i();e.useEffect((()=>{if(l){if(n(l.status||"public"),l.scheduledFor){const e=new Date(l.scheduledFor);b(e.toISOString().split("T")[0]),p(e.toTimeString().slice(0,5))}else{const e=new Date;e.setDate(e.getDate()+1),e.setHours(12,0,0,0),b(e.toISOString().split("T")[0]),p("12:00")}N(null),C(!1)}}),[l]);return t?s.jsx("div",{className:"fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/70",children:s.jsxs("div",{className:"bg-gray-800 rounded-lg w-full max-w-md",children:[s.jsxs("div",{className:"flex items-center justify-between p-4 border-b border-gray-700",children:[s.jsx("h2",{className:"text-xl font-bold text-white",children:"Change Video Status"}),s.jsx("button",{className:"p-1 rounded-full hover:bg-gray-700 text-gray-400 hover:text-white transition-colors",onClick:r,disabled:j,children:s.jsx(o,{size:20})})]}),s.jsxs("form",{onSubmit:async e=>{e.preventDefault(),f(!0),N(null),C(!1);try{let e;if("scheduled"===d){if(!c)return N("Please select a publication date"),void f(!1);const s=new Date(`${c}T${y||"12:00"}`);if(s<=new Date)return N("Scheduled date must be in the future"),void f(!1);e=s.toISOString()}await S(l.id,d,e)?(C(!0),setTimeout((()=>{r()}),1500)):N("Failed to update video status. Please try again.")}catch(s){N("An unexpected error occurred. Please try again.")}finally{f(!1)}},className:"p-4",children:[s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-200 mb-2",children:"Video Status"}),s.jsxs("div",{className:"grid grid-cols-3 gap-3",children:[s.jsxs("button",{type:"button",className:`flex flex-col items-center justify-center p-3 rounded-md border ${"public"===d?"border-green-500 bg-green-500/10 text-green-400":"border-gray-600 bg-gray-700 text-gray-300 hover:bg-gray-600"} transition-colors`,onClick:()=>n("public"),children:[s.jsx(u,{size:24,className:"mb-2"}),s.jsx("span",{className:"text-sm font-medium",children:"Public"})]}),s.jsxs("button",{type:"button",className:`flex flex-col items-center justify-center p-3 rounded-md border ${"private"===d?"border-gray-500 bg-gray-500/10 text-gray-300":"border-gray-600 bg-gray-700 text-gray-300 hover:bg-gray-600"} transition-colors`,onClick:()=>n("private"),children:[s.jsx(g,{size:24,className:"mb-2"}),s.jsx("span",{className:"text-sm font-medium",children:"Private"})]}),s.jsxs("button",{type:"button",className:`flex flex-col items-center justify-center p-3 rounded-md border ${"scheduled"===d?"border-blue-500 bg-blue-500/10 text-blue-400":"border-gray-600 bg-gray-700 text-gray-300 hover:bg-gray-600"} transition-colors`,onClick:()=>n("scheduled"),children:[s.jsx(h,{size:24,className:"mb-2"}),s.jsx("span",{className:"text-sm font-medium",children:"Scheduled"})]})]})]}),"scheduled"===d&&s.jsxs("div",{className:"bg-gray-700 p-4 rounded-md",children:[s.jsx("label",{className:"block text-sm font-medium text-gray-200 mb-2",children:"Publication Date and Time"}),s.jsxs("div",{className:"grid grid-cols-2 gap-3",children:[s.jsx("div",{children:s.jsx("input",{type:"date",className:"w-full rounded-md bg-gray-600 border border-gray-600 text-white focus:border-blue-500 focus:ring-2 focus:ring-blue-500 focus:ring-opacity-20 p-2 transition-colors",value:c,onChange:e=>b(e.target.value),min:(new Date).toISOString().split("T")[0]})}),s.jsx("div",{children:s.jsx("input",{type:"time",className:"w-full rounded-md bg-gray-600 border border-gray-600 text-white focus:border-blue-500 focus:ring-2 focus:ring-blue-500 focus:ring-opacity-20 p-2 transition-colors",value:y,onChange:e=>p(e.target.value)})})]}),s.jsx("p",{className:"text-sm text-gray-400 mt-2",children:"Your video will be automatically published at the specified date and time."})]}),"public"===d&&s.jsx("div",{className:"bg-green-500/10 border border-green-500/30 rounded-md p-3 text-sm text-green-300",children:s.jsx("p",{children:"Your video will be visible to everyone."})}),"private"===d&&s.jsx("div",{className:"bg-gray-700 border border-gray-600 rounded-md p-3 text-sm text-gray-300",children:s.jsx("p",{children:"Your video will only be visible to you."})}),v&&s.jsxs("div",{className:"bg-red-500/20 border border-red-500 rounded-md p-3 flex items-start",children:[s.jsx(x,{size:20,className:"text-red-500 mr-2 flex-shrink-0 mt-0.5"}),s.jsx("p",{className:"text-red-200",children:v})]}),w&&s.jsxs("div",{className:"bg-green-500/20 border border-green-500 rounded-md p-3 flex items-start",children:[s.jsx(x,{size:20,className:"text-green-500 mr-2 flex-shrink-0 mt-0.5"}),s.jsx("p",{className:"text-green-200",children:"Video status updated successfully!"})]})]}),s.jsxs("div",{className:"flex justify-end space-x-3 mt-6",children:[s.jsx(a,{variant:"ghost",type:"button",onClick:r,disabled:j,children:"Cancel"}),s.jsx(a,{variant:"primary",type:"submit",leftIcon:s.jsx(m,{size:18}),isLoading:j,disabled:j,children:"Save Changes"})]})]})]})}):null},A=({isOpen:t,onClose:r,operation:l,selectedCount:d})=>{const[n,c]=e.useState("public"),[p,j]=e.useState((()=>{const e=new Date;return e.setDate(e.getDate()+1),e.toISOString().split("T")[0]})),[f,v]=e.useState("12:00"),[N,w]=e.useState(""),[C,S]=e.useState(!1),[k,z]=e.useState(null),[D,V]=e.useState(!1),{batchDeleteVideos:P,batchUpdateVideosStatus:O,batchUpdateVideosCategory:B}=i();return t?s.jsx("div",{className:"fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/70",children:s.jsxs("div",{className:"bg-gray-800 rounded-lg w-full max-w-md",children:[s.jsxs("div",{className:"flex items-center justify-between p-4 border-b border-gray-700",children:[s.jsxs("h2",{className:"text-xl font-bold text-white flex items-center",children:[(()=>{switch(l){case"delete":return s.jsx(b,{size:20,className:"text-red-500 mr-2"});case"status":return s.jsx(u,{size:20,className:"text-blue-500 mr-2"});case"category":return s.jsx(y,{size:20,className:"text-purple-500 mr-2"});default:return null}})(),(()=>{switch(l){case"delete":return"Delete Selected Videos";case"status":return"Change Video Status";case"category":return"Change Video Category";default:return"Batch Operation"}})()]}),s.jsx("button",{className:"p-1 rounded-full hover:bg-gray-700 text-gray-400 hover:text-white transition-colors",onClick:r,disabled:C,children:s.jsx(o,{size:20})})]}),s.jsxs("form",{onSubmit:async e=>{if(e.preventDefault(),0!==d){S(!0),z(null),V(!1);try{let e=!1;switch(l){case"delete":e=await P();break;case"status":e=await O(n);break;case"category":if(!N)return z("Please select a category"),void S(!1);e=await B(N)}e?(V(!0),setTimeout((()=>{r()}),1500)):z("Operation failed. Please try again.")}catch(s){z("An unexpected error occurred. Please try again.")}finally{S(!1)}}else z("No videos selected")},className:"p-4",children:[s.jsxs("div",{className:"space-y-4",children:["delete"===l&&s.jsx("div",{className:"bg-red-500/10 border border-red-500/30 rounded-md p-4",children:s.jsxs("div",{className:"flex items-start",children:[s.jsx(x,{size:24,className:"text-red-500 mr-3 flex-shrink-0 mt-0.5"}),s.jsxs("div",{children:[s.jsx("h3",{className:"text-lg font-medium text-red-300 mb-1",children:"Confirm Deletion"}),s.jsxs("p",{className:"text-red-200",children:["Are you sure you want to delete ",d," selected video",1!==d?"s":"","? This action cannot be undone."]})]})]})}),"status"===l&&s.jsxs(s.Fragment,{children:[s.jsxs("div",{children:[s.jsxs("label",{className:"block text-sm font-medium text-gray-200 mb-2",children:["New Status for ",d," video",1!==d?"s":""]}),s.jsxs("div",{className:"grid grid-cols-3 gap-3",children:[s.jsxs("button",{type:"button",className:`flex flex-col items-center justify-center p-3 rounded-md border ${"public"===n?"border-green-500 bg-green-500/10 text-green-400":"border-gray-600 bg-gray-700 text-gray-300 hover:bg-gray-600"} transition-colors`,onClick:()=>c("public"),children:[s.jsx(u,{size:24,className:"mb-2"}),s.jsx("span",{className:"text-sm font-medium",children:"Public"})]}),s.jsxs("button",{type:"button",className:`flex flex-col items-center justify-center p-3 rounded-md border ${"private"===n?"border-gray-500 bg-gray-500/10 text-gray-300":"border-gray-600 bg-gray-700 text-gray-300 hover:bg-gray-600"} transition-colors`,onClick:()=>c("private"),children:[s.jsx(g,{size:24,className:"mb-2"}),s.jsx("span",{className:"text-sm font-medium",children:"Private"})]}),s.jsxs("button",{type:"button",className:`flex flex-col items-center justify-center p-3 rounded-md border ${"scheduled"===n?"border-blue-500 bg-blue-500/10 text-blue-400":"border-gray-600 bg-gray-700 text-gray-300 hover:bg-gray-600"} transition-colors`,onClick:()=>c("scheduled"),children:[s.jsx(h,{size:24,className:"mb-2"}),s.jsx("span",{className:"text-sm font-medium",children:"Scheduled"})]})]})]}),"scheduled"===n&&s.jsxs("div",{className:"bg-gray-700 p-4 rounded-md",children:[s.jsx("label",{className:"block text-sm font-medium text-gray-200 mb-2",children:"Publication Date and Time"}),s.jsxs("div",{className:"grid grid-cols-2 gap-3",children:[s.jsx("div",{children:s.jsx("input",{type:"date",className:"w-full rounded-md bg-gray-600 border border-gray-600 text-white focus:border-blue-500 focus:ring-2 focus:ring-blue-500 focus:ring-opacity-20 p-2 transition-colors",value:p,onChange:e=>j(e.target.value),min:(new Date).toISOString().split("T")[0]})}),s.jsx("div",{children:s.jsx("input",{type:"time",className:"w-full rounded-md bg-gray-600 border border-gray-600 text-white focus:border-blue-500 focus:ring-2 focus:ring-blue-500 focus:ring-opacity-20 p-2 transition-colors",value:f,onChange:e=>v(e.target.value)})})]})]})]}),"category"===l&&s.jsxs("div",{children:[s.jsxs("label",{className:"block text-sm font-medium text-gray-200 mb-2",children:["New Category for ",d," video",1!==d?"s":""]}),s.jsxs("select",{className:"w-full rounded-md bg-gray-700 border border-gray-600 text-white focus:border-purple-500 focus:ring-2 focus:ring-purple-500 focus:ring-opacity-20 p-3 transition-colors",value:N,onChange:e=>w(e.target.value),children:[s.jsx("option",{value:"",children:"Select a category"}),T.map((e=>s.jsx("option",{value:e.slug,children:e.name},e.id)))]})]}),k&&s.jsxs("div",{className:"bg-red-500/20 border border-red-500 rounded-md p-3 flex items-start",children:[s.jsx(x,{size:20,className:"text-red-500 mr-2 flex-shrink-0 mt-0.5"}),s.jsx("p",{className:"text-red-200",children:k})]}),D&&s.jsxs("div",{className:"bg-green-500/20 border border-green-500 rounded-md p-3 flex items-start",children:[s.jsx(x,{size:20,className:"text-green-500 mr-2 flex-shrink-0 mt-0.5"}),s.jsx("p",{className:"text-green-200",children:"Operation completed successfully!"})]})]}),s.jsxs("div",{className:"flex justify-end space-x-3 mt-6",children:[s.jsx(a,{variant:"ghost",type:"button",onClick:r,disabled:C,children:"Cancel"}),s.jsx(a,{variant:"delete"===l?"danger":"primary",type:"submit",leftIcon:"delete"===l?s.jsx(b,{size:18}):s.jsx(m,{size:18}),isLoading:C,disabled:C||0===d,children:"delete"===l?"Delete":"Save Changes"})]})]})]})}):null},I=({isOpen:t,onClose:r,video:l})=>{const[n,c]=e.useState(null),[m,g]=e.useState(!1),[h,b]=e.useState(null),{fetchVideoAnalytics:y}=i();return e.useEffect((()=>{if(t&&l){(async()=>{g(!0),b(null);try{const e=await y(l.id);c(e)}catch(e){b("Failed to load analytics data")}finally{g(!1)}})()}}),[t,l,y]),t?s.jsx("div",{className:"fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/70",children:s.jsxs("div",{className:"bg-gray-800 rounded-lg w-full max-w-4xl max-h-[90vh] overflow-y-auto",children:[s.jsxs("div",{className:"flex items-center justify-between p-4 border-b border-gray-700",children:[s.jsxs("h2",{className:"text-xl font-bold text-white flex items-center",children:[s.jsx(p,{size:20,className:"text-blue-700 mr-2"}),"Video Analytics: ",l.title]}),s.jsx("button",{className:"p-1 rounded-full hover:bg-gray-700 text-gray-400 hover:text-white transition-colors",onClick:r,children:s.jsx(o,{size:20})})]}),s.jsxs("div",{className:"p-4",children:[m?s.jsx("div",{className:"flex justify-center items-center h-64",children:s.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-orange-500"})}):h?s.jsxs("div",{className:"bg-red-500/20 border border-red-500 rounded-md p-4 flex items-start",children:[s.jsx(x,{size:24,className:"text-red-500 mr-3 flex-shrink-0 mt-0.5"}),s.jsxs("div",{children:[s.jsx("h3",{className:"text-lg font-medium text-red-300 mb-1",children:"Error Loading Analytics"}),s.jsx("p",{className:"text-red-200",children:h})]})]}):n?s.jsxs("div",{className:"space-y-6",children:[s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[s.jsxs("div",{className:"bg-gray-700 rounded-lg p-4",children:[s.jsxs("div",{className:"flex items-center mb-2",children:[s.jsx(u,{size:18,className:"text-blue-400 mr-2"}),s.jsx("h3",{className:"text-gray-300 font-medium",children:"Total Views"})]}),s.jsx("div",{className:"text-2xl font-bold text-white",children:d(l.views)}),s.jsxs("div",{className:"text-sm text-green-400 mt-1 flex items-center",children:[s.jsx(j,{size:14,className:"mr-1"}),"+",d(n.viewsLast7Days)," in last 7 days"]})]}),s.jsxs("div",{className:"bg-gray-700 rounded-lg p-4",children:[s.jsxs("div",{className:"flex items-center mb-2",children:[s.jsx(f,{size:18,className:"text-green-400 mr-2"}),s.jsx("h3",{className:"text-gray-300 font-medium",children:"Total Likes"})]}),s.jsx("div",{className:"text-2xl font-bold text-white",children:d(l.likes)}),s.jsxs("div",{className:"text-sm text-green-400 mt-1 flex items-center",children:[s.jsx(j,{size:14,className:"mr-1"}),"+",d(n.likesLast7Days)," in last 7 days"]})]}),s.jsxs("div",{className:"bg-gray-700 rounded-lg p-4",children:[s.jsxs("div",{className:"flex items-center mb-2",children:[s.jsx(v,{size:18,className:"text-purple-400 mr-2"}),s.jsx("h3",{className:"text-gray-300 font-medium",children:"Top Country"})]}),s.jsx("div",{className:"text-2xl font-bold text-white",children:n.viewsByCountry&&n.viewsByCountry.length>0?n.viewsByCountry[0].country:"N/A"}),s.jsx("div",{className:"text-sm text-gray-400 mt-1",children:n.viewsByCountry&&n.viewsByCountry.length>0?`${d(n.viewsByCountry[0].count)} views`:"No data available"})]}),s.jsxs("div",{className:"bg-gray-700 rounded-lg p-4",children:[s.jsxs("div",{className:"flex items-center mb-2",children:[s.jsx(N,{size:18,className:"text-orange-400 mr-2"}),s.jsx("h3",{className:"text-gray-300 font-medium",children:"Top Device"})]}),s.jsx("div",{className:"text-2xl font-bold text-white",children:n.viewsByDevice&&n.viewsByDevice.length>0?n.viewsByDevice[0].device:"N/A"}),s.jsx("div",{className:"text-sm text-gray-400 mt-1",children:n.viewsByDevice&&n.viewsByDevice.length>0?`${d(n.viewsByDevice[0].count)} views`:"No data available"})]})]}),s.jsxs("div",{className:"bg-gray-700 rounded-lg p-4",children:[s.jsx("h3",{className:"text-lg font-medium text-white mb-4",children:"Views Over Time"}),s.jsx("div",{className:"h-64 relative",children:s.jsx("div",{className:"absolute inset-0 flex items-end",children:n.viewsByDay.map(((e,t)=>{const a=Math.max(...n.viewsByDay.map((e=>e.count))),r=a>0?e.count/a*100:0;return s.jsxs("div",{className:"flex-1 flex flex-col items-center",children:[s.jsx("div",{className:"w-full bg-orange-500 rounded-t",style:{height:`${r}%`}}),t%5==0&&s.jsx("div",{className:"text-xs text-gray-400 mt-1 rotate-45 origin-left",children:new Date(e.date).toLocaleDateString(void 0,{month:"short",day:"numeric"})})]},t)}))})})]}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[s.jsxs("div",{className:"bg-gray-700 rounded-lg p-4",children:[s.jsx("h3",{className:"text-lg font-medium text-white mb-4",children:"Top Countries"}),s.jsx("div",{className:"space-y-2",children:n.viewsByCountry?.map(((e,t)=>s.jsxs("div",{className:"flex items-center",children:[s.jsx("div",{className:"w-32 text-gray-300",children:e.country}),s.jsx("div",{className:"flex-1 h-4 bg-gray-600 rounded-full overflow-hidden",children:s.jsx("div",{className:"h-full bg-blue-500 rounded-full",style:{width:(n.viewsByCountry?e.count/n.viewsByCountry[0].count*100:0)+"%"}})}),s.jsx("div",{className:"w-16 text-right text-gray-400 text-sm",children:d(e.count)})]},t)))})]}),s.jsxs("div",{className:"bg-gray-700 rounded-lg p-4",children:[s.jsx("h3",{className:"text-lg font-medium text-white mb-4",children:"Devices"}),s.jsx("div",{className:"space-y-2",children:n.viewsByDevice?.map(((e,t)=>s.jsxs("div",{className:"flex items-center",children:[s.jsx("div",{className:"w-32 text-gray-300",children:e.device}),s.jsx("div",{className:"flex-1 h-4 bg-gray-600 rounded-full overflow-hidden",children:s.jsx("div",{className:"h-full bg-green-500 rounded-full",style:{width:(n.viewsByDevice?e.count/n.viewsByDevice[0].count*100:0)+"%"}})}),s.jsx("div",{className:"w-16 text-right text-gray-400 text-sm",children:d(e.count)})]},t)))})]})]})]}):s.jsx("div",{className:"text-center py-8 text-gray-400",children:"No analytics data available"}),s.jsx("div",{className:"flex justify-end mt-6",children:s.jsx(a,{variant:"primary",onClick:r,children:"Close"})})]})]})}):null},L=()=>{const t=P(),{user:x,isLoading:m}=n(),{userVideos:j,selectedVideoIds:f,sortOptions:v,filterOptions:N,pagination:L,isLoading:E,error:$,fetchUserVideos:F,deleteVideo:U,selectVideo:Y,selectAllVideos:G,batchDeleteVideos:H,batchUpdateVideosStatus:M,batchUpdateVideosCategory:R,setSortOptions:q,setFilterOptions:J,setPage:W}=i(),[K,Q]=e.useState(null),[X,Z]=e.useState(!1),[_,ee]=e.useState(!1),[se,te]=e.useState(!1),[ae,re]=e.useState(!1),[le,ie]=e.useState(!1),[de,ne]=e.useState(!1),[ce,oe]=e.useState(!1),[xe,me]=e.useState(""),[ue,ge]=e.useState(null),[he,be]=e.useState(!1),[ye,pe]=e.useState("delete");e.useEffect((()=>{m||x||t("/")}),[x,m,t]),e.useEffect((()=>{x&&F(v,N,L.currentPage)}),[x,F,v,N,L.currentPage]);const je=e=>{pe(e),ie(!0)},fe=e=>{const s=v.field===e&&"asc"===v.direction?"desc":"asc";q({field:e,direction:s}),oe(!1)},ve=e=>{J({...N,...e}),ne(!1)};if(m||E&&0===j.length)return s.jsx("div",{className:"container mx-auto px-4 py-8",children:s.jsx("div",{className:"flex justify-center items-center h-64",children:s.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-orange-500"})})});if($&&!j.length)return s.jsx("div",{className:"container mx-auto px-4 py-8",children:s.jsxs("div",{className:"bg-red-500/20 border border-red-500 rounded-md p-4 max-w-2xl mx-auto",children:[s.jsx("h2",{className:"text-xl font-bold text-white mb-2",children:"Error Loading Videos"}),s.jsx("p",{className:"text-red-200",children:$})]})});const Ne=e=>{switch(e){case"public":return s.jsx("span",{className:"px-2 py-0.5 bg-green-500/20 text-green-400 rounded text-xs",children:"Public"});case"private":return s.jsx("span",{className:"px-2 py-0.5 bg-gray-500/20 text-gray-400 rounded text-xs",children:"Private"});case"scheduled":return s.jsx("span",{className:"px-2 py-0.5 bg-blue-500/20 text-blue-400 rounded text-xs",children:"Scheduled"});default:return null}};return s.jsxs("div",{className:"container mx-auto px-4 py-8",children:[s.jsxs("div",{className:"flex flex-col md:flex-row md:justify-between md:items-center mb-6 gap-4",children:[s.jsx("h1",{className:"text-2xl md:text-3xl font-bold",children:"Manage Your Videos"}),x&&s.jsx("div",{className:"flex items-center space-x-2",children:s.jsx(a,{variant:"primary",leftIcon:s.jsx(w,{size:18}),onClick:()=>t("/upload"),children:"Upload New Video"})})]}),0===j.length?s.jsxs("div",{className:"bg-gray-800 rounded-lg p-8 text-center",children:[s.jsx("h2",{className:"text-xl font-semibold text-white mb-4",children:"You haven't uploaded any videos yet"}),s.jsx("p",{className:"text-gray-400 mb-6",children:"Get started by uploading your first video"}),x&&s.jsx(a,{variant:"primary",leftIcon:s.jsx(w,{size:18}),onClick:()=>t("/upload"),children:"Upload Video"})]}):s.jsxs(s.Fragment,{children:[s.jsxs("div",{className:"bg-gray-800 rounded-lg p-4 mb-4",children:[s.jsxs("div",{className:"flex flex-col md:flex-row gap-4",children:[s.jsx("div",{className:"flex-1",children:s.jsxs("form",{onSubmit:e=>{e.preventDefault(),J({...N,search:xe})},className:"relative",children:[s.jsx("input",{type:"text",placeholder:"Search videos...",className:"w-full bg-gray-700 border border-gray-600 rounded-md py-2 pl-10 pr-4 text-white focus:outline-none focus:ring-2 focus:ring-orange-500",value:xe,onChange:e=>me(e.target.value)}),s.jsx(C,{className:"absolute left-3 top-2.5 text-gray-400",size:18}),xe&&s.jsx("button",{type:"button",className:"absolute right-3 top-2.5 text-gray-400 hover:text-white",onClick:()=>{me(""),J({...N,search:void 0})},children:s.jsx(o,{size:18})})]})}),s.jsxs("div",{className:"flex gap-2",children:[s.jsxs("div",{className:"relative",children:[s.jsxs(a,{variant:"secondary",leftIcon:s.jsx(S,{size:18}),onClick:()=>oe(!ce),className:"whitespace-nowrap",children:["Sort",s.jsx(k,{size:16,className:"ml-1"})]}),ce&&s.jsx("div",{className:"absolute right-0 mt-2 w-48 bg-gray-800 border border-gray-700 rounded-md shadow-lg z-10",children:s.jsxs("div",{className:"py-1",children:[s.jsxs("button",{className:"w-full text-left px-4 py-2 text-sm hover:bg-gray-700 "+("title"===v.field?"text-orange-500":"text-white"),onClick:()=>fe("title"),children:["Title ","title"===v.field&&("asc"===v.direction?"↑":"↓")]}),s.jsxs("button",{className:"w-full text-left px-4 py-2 text-sm hover:bg-gray-700 "+("views"===v.field?"text-orange-500":"text-white"),onClick:()=>fe("views"),children:["Views ","views"===v.field&&("asc"===v.direction?"↑":"↓")]}),s.jsxs("button",{className:"w-full text-left px-4 py-2 text-sm hover:bg-gray-700 "+("createdAt"===v.field?"text-orange-500":"text-white"),onClick:()=>fe("createdAt"),children:["Date ","createdAt"===v.field&&("asc"===v.direction?"↑":"↓")]}),s.jsxs("button",{className:"w-full text-left px-4 py-2 text-sm hover:bg-gray-700 "+("status"===v.field?"text-orange-500":"text-white"),onClick:()=>fe("status"),children:["Status ","status"===v.field&&("asc"===v.direction?"↑":"↓")]})]})})]}),s.jsxs("div",{className:"relative",children:[s.jsxs(a,{variant:"secondary",leftIcon:s.jsx(y,{size:18}),onClick:()=>ne(!de),className:"whitespace-nowrap "+(Object.keys(N).length>0?"border-orange-500":""),children:["Filter",s.jsx(k,{size:16,className:"ml-1"})]}),de&&s.jsx("div",{className:"absolute right-0 mt-2 w-48 bg-gray-800 border border-gray-700 rounded-md shadow-lg z-10",children:s.jsxs("div",{className:"py-1",children:[s.jsx("div",{className:"px-4 py-2 text-sm text-gray-300 font-medium border-b border-gray-700",children:"Status"}),s.jsx("button",{className:"w-full text-left px-4 py-2 text-sm hover:bg-gray-700 "+("public"===N.status?"text-orange-500":"text-white"),onClick:()=>ve({status:"public"}),children:"Public"}),s.jsx("button",{className:"w-full text-left px-4 py-2 text-sm hover:bg-gray-700 "+("private"===N.status?"text-orange-500":"text-white"),onClick:()=>ve({status:"private"}),children:"Private"}),s.jsx("button",{className:"w-full text-left px-4 py-2 text-sm hover:bg-gray-700 "+("scheduled"===N.status?"text-orange-500":"text-white"),onClick:()=>ve({status:"scheduled"}),children:"Scheduled"}),s.jsx("div",{className:"px-4 py-2 text-sm text-gray-300 font-medium border-b border-t border-gray-700",children:"Category"}),T.map((e=>s.jsx("button",{className:"w-full text-left px-4 py-2 text-sm hover:bg-gray-700 "+(N.category===e.slug?"text-orange-500":"text-white"),onClick:()=>ve({category:e.slug}),children:e.name},e.id))),s.jsx("div",{className:"border-t border-gray-700 mt-1 pt-1",children:s.jsx("button",{className:"w-full text-left px-4 py-2 text-sm text-orange-500 hover:bg-gray-700",onClick:()=>{J({}),me("")},children:"Clear Filters"})})]})})]}),s.jsx(a,{variant:"secondary",leftIcon:s.jsx(z,{size:18}),onClick:()=>F(),isLoading:E,disabled:E,className:"whitespace-nowrap",children:"Refresh"})]})]}),f.length>0&&s.jsxs("div",{className:"mt-4 p-3 bg-gray-700 rounded-md flex flex-wrap items-center gap-3",children:[s.jsxs("div",{className:"text-white font-medium",children:[f.length," video",1!==f.length?"s":""," selected"]}),s.jsx("div",{className:"flex-1"}),s.jsx(a,{variant:"secondary",size:"sm",leftIcon:s.jsx(u,{size:16}),onClick:()=>je("status"),children:"Change Status"}),s.jsx(a,{variant:"secondary",size:"sm",leftIcon:s.jsx(y,{size:16}),onClick:()=>je("category"),children:"Change Category"}),s.jsx(a,{variant:"danger",size:"sm",leftIcon:s.jsx(b,{size:16}),onClick:()=>je("delete"),children:"Delete Selected"})]})]}),s.jsxs("div",{className:"bg-gray-800 rounded-lg overflow-hidden",children:[s.jsx("div",{className:"overflow-x-auto",children:s.jsxs("table",{className:"w-full",children:[s.jsx("thead",{className:"bg-gray-700",children:s.jsxs("tr",{children:[s.jsx("th",{className:"px-4 py-3 text-left",children:s.jsx("div",{className:"flex items-center",children:s.jsx("input",{type:"checkbox",className:"rounded bg-gray-700 border-gray-600 text-orange-500 focus:ring-orange-500",onChange:e=>{G(e.target.checked)},checked:f.length>0&&f.length===j.length})})}),s.jsx("th",{className:"px-4 py-3 text-left text-sm font-medium text-gray-200",children:"Video"}),s.jsx("th",{className:"px-4 py-3 text-left text-sm font-medium text-gray-200 hidden md:table-cell",children:"Status"}),s.jsx("th",{className:"px-4 py-3 text-left text-sm font-medium text-gray-200 hidden md:table-cell",children:"Date"}),s.jsx("th",{className:"px-4 py-3 text-left text-sm font-medium text-gray-200 hidden md:table-cell",children:"Views"}),s.jsx("th",{className:"px-4 py-3 text-right text-sm font-medium text-gray-200",children:"Actions"})]})}),s.jsx("tbody",{className:"divide-y divide-gray-700",children:j.map((e=>s.jsxs("tr",{className:"hover:bg-gray-700/50 transition-colors "+(f.includes(e.id)?"bg-gray-700/70":""),children:[s.jsx("td",{className:"px-4 py-4",children:s.jsx("input",{type:"checkbox",className:"rounded bg-gray-700 border-gray-600 text-orange-500 focus:ring-orange-500",checked:f.includes(e.id),onChange:s=>((e,s)=>{Y(e.id,s)})(e,s.target.checked)})}),s.jsx("td",{className:"px-4 py-4",children:s.jsxs("div",{className:"flex items-center",children:[s.jsxs("div",{className:"relative w-24 h-14 flex-shrink-0 mr-3",children:[s.jsx("img",{src:e.thumbnailUrl||"https://placehold.co/640x360/gray/white?text=No+Thumbnail",alt:e.title,className:"w-full h-full object-cover rounded",onError:e=>{e.currentTarget.src="https://placehold.co/640x360/gray/white?text=No+Thumbnail"}}),e.duration>0&&s.jsx("div",{className:"absolute bottom-1 right-1 bg-black/80 text-white text-xs px-1 rounded",children:c(e.duration)})]}),s.jsxs("div",{className:"flex-1 min-w-0",children:[s.jsx("h3",{className:"text-white font-medium truncate",children:e.title}),s.jsx("p",{className:"text-gray-400 text-sm truncate",children:e.description})]})]})}),s.jsx("td",{className:"px-4 py-4 text-sm hidden md:table-cell",children:Ne(e.status)}),s.jsx("td",{className:"px-4 py-4 text-sm text-gray-400 hidden md:table-cell",children:new Date(e.createdAt).toLocaleDateString()}),s.jsx("td",{className:"px-4 py-4 text-sm text-gray-400 hidden md:table-cell",children:d(e.views||0)}),s.jsx("td",{className:"px-4 py-4 text-right",children:s.jsxs("div",{className:"flex items-center justify-end space-x-2",children:[s.jsx("button",{className:"p-1.5 rounded-full bg-blue-500/20 text-blue-400 hover:bg-blue-500/30 transition-colors",onClick:()=>(e=>{t(`/video/${e.id}`)})(e),title:"Play",children:s.jsx(D,{size:16})}),s.jsx("button",{className:"p-1.5 rounded-full bg-green-500/20 text-green-400 hover:bg-green-500/30 transition-colors",onClick:()=>(e=>{Q(e),re(!0)})(e),title:"Analytics",children:s.jsx(p,{size:16})}),s.jsx("button",{className:"p-1.5 rounded-full bg-purple-500/20 text-purple-400 hover:bg-purple-500/30 transition-colors",onClick:()=>(e=>{Q(e),te(!0)})(e),title:"Change Status",children:"public"===e.status?s.jsx(u,{size:16}):"private"===e.status?s.jsx(g,{size:16}):s.jsx(h,{size:16})}),s.jsx("button",{className:"p-1.5 rounded-full bg-orange-500/20 text-orange-400 hover:bg-orange-500/30 transition-colors",onClick:()=>(e=>{Q(e),Z(!0)})(e),title:"Edit",children:s.jsx(V,{size:16})}),s.jsx("button",{className:"p-1.5 rounded-full bg-red-500/20 text-red-400 hover:bg-red-500/30 transition-colors",onClick:()=>(e=>{Q(e),ee(!0)})(e),title:"Delete",children:s.jsx(b,{size:16})})]})})]},e.id)))})]})}),!E&&j.length>0&&s.jsx("div",{className:"mt-6 flex justify-center",children:s.jsx(r,{currentPage:L.currentPage,totalPages:L.totalPages,onPageChange:e=>W(e),className:"mb-4"})})]})]}),K&&s.jsx(O,{isOpen:X,onClose:()=>Z(!1),video:K}),s.jsx(l,{isOpen:_,onClose:()=>ee(!1),onConfirm:async()=>{if(K){be(!0),ge(null);try{await U(K.id)?(ee(!1),Q(null)):ge("Failed to delete video. Please try again.")}catch(e){ge("An unexpected error occurred. Please try again.")}finally{be(!1)}}},title:"Delete Video",message:`Are you sure you want to delete "${K?.title}"? This action cannot be undone.`,isLoading:he,error:ue}),K&&s.jsx(B,{isOpen:se,onClose:()=>te(!1),video:K}),K&&s.jsx(I,{isOpen:ae,onClose:()=>re(!1),video:K}),s.jsx(A,{isOpen:le,onClose:()=>ie(!1),operation:ye,selectedCount:f.length})]})};export{L as default};
