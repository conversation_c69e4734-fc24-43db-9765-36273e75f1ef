/**
 * New Upload Store using MySQL API
 * Replaces Supabase file upload functionality
 */

import { create } from 'zustand';
import { apiClient } from '../lib/api';
import { useAuthStoreNew } from './authStoreNew';

interface UploadState {
  isUploading: boolean;
  uploadProgress: number;
  uploadError: string | null;
  uploadedVideoUrl: string | null;
  uploadedThumbnailUrl: string | null;
  
  // Actions
  uploadVideo: (
    videoFile: File,
    thumbnailFile: File | null,
    title: string,
    description: string,
    category: string
  ) => Promise<void>;
  clearUpload: () => void;
  setUploadProgress: (progress: number) => void;
}

export const useUploadStoreNew = create<UploadState>((set, get) => ({
  isUploading: false,
  uploadProgress: 0,
  uploadError: null,
  uploadedVideoUrl: null,
  uploadedThumbnailUrl: null,

  uploadVideo: async (
    videoFile: File,
    thumbnailFile: File | null,
    title: string,
    description: string,
    category: string
  ) => {
    try {
      // Check if user is authenticated
      const { user } = useAuthStoreNew.getState();
      if (!user) {
        throw new Error('You must be logged in to upload videos');
      }

      set({
        isUploading: true,
        uploadProgress: 0,
        uploadError: null,
        uploadedVideoUrl: null,
        uploadedThumbnailUrl: null,
      });

      // Step 1: Upload video file (0-70% progress)
      console.log('📤 Uploading video file...');
      set({ uploadProgress: 5 });

      const videoUploadResponse = await apiClient.uploadFile(videoFile, 'video');
      
      if (!videoUploadResponse.success) {
        throw new Error('Failed to upload video file');
      }

      const videoUrl = videoUploadResponse.data.url;
      set({ uploadProgress: 70, uploadedVideoUrl: videoUrl });

      // Step 2: Upload thumbnail if provided (70-85% progress)
      let thumbnailUrl: string | null = null;
      
      if (thumbnailFile) {
        console.log('📤 Uploading thumbnail...');
        set({ uploadProgress: 75 });

        try {
          const thumbnailUploadResponse = await apiClient.uploadFile(thumbnailFile, 'thumbnail');
          
          if (thumbnailUploadResponse.success) {
            thumbnailUrl = thumbnailUploadResponse.data.url;
            set({ uploadedThumbnailUrl: thumbnailUrl });
          }
        } catch (thumbnailError) {
          console.warn('Thumbnail upload failed, continuing without thumbnail:', thumbnailError);
          // Continue without thumbnail rather than failing the entire upload
        }
      }

      set({ uploadProgress: 85 });

      // Step 3: Create video record in database (85-100% progress)
      console.log('💾 Creating video record...');
      set({ uploadProgress: 90 });

      const createVideoResponse = await apiClient.createVideo({
        title,
        description,
        video_url: videoUrl,
        thumbnail_url: thumbnailUrl || undefined,
        category,
        duration: 0, // Will be updated later if needed
      });

      if (!createVideoResponse.success) {
        throw new Error('Failed to create video record');
      }

      set({ uploadProgress: 100 });

      console.log('✅ Video upload completed successfully!');
      
      // Keep the upload state for a moment to show success
      setTimeout(() => {
        set({
          isUploading: false,
          uploadProgress: 0,
        });
      }, 2000);

    } catch (error) {
      console.error('❌ Upload failed:', error);
      set({
        isUploading: false,
        uploadError: error instanceof Error ? error.message : 'Upload failed',
        uploadProgress: 0,
      });
      throw error;
    }
  },

  clearUpload: () => {
    set({
      isUploading: false,
      uploadProgress: 0,
      uploadError: null,
      uploadedVideoUrl: null,
      uploadedThumbnailUrl: null,
    });
  },

  setUploadProgress: (progress: number) => {
    set({ uploadProgress: Math.min(100, Math.max(0, progress)) });
  },
}));
