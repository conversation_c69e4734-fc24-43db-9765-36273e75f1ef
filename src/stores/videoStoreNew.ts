/**
 * New Video Store using MySQL API
 * Replaces Supabase video queries
 */

import { create } from 'zustand';
import { apiClient, Video } from '../lib/api';

interface VideoItem {
  id: string;
  title: string;
  description: string;
  thumbnail: string;
  videoUrl: string;
  duration: number;
  views: number;
  likes: number;
  isHD: boolean;
  category: string;
  uploadDate: string;
  creator: {
    id: string;
    name: string;
    avatar: string;
    isVerified: boolean;
    isCreator: boolean;
    subscriberCount: number;
  };
}

interface Pagination {
  currentPage: number;
  totalPages: number;
  totalCount: number;
  pageSize: number;
}

interface VideoState {
  videos: VideoItem[];
  pagination: Pagination;
  isLoading: boolean;
  error: string | null;
  currentCategory: string;
  searchQuery: string;
  
  // Actions
  fetchVideos: (category?: string, page?: number) => Promise<void>;
  searchVideos: (query: string, page?: number) => Promise<void>;
  setCategory: (category: string) => void;
  setPage: (page: number) => Promise<void>;
  clearError: () => void;
}

// Helper function to convert API video to VideoItem
const convertApiVideoToVideoItem = (video: Video): VideoItem => ({
  id: video.id,
  title: video.title,
  description: video.description || '',
  thumbnail: video.thumbnail_url || 'https://placehold.co/400x225/gray/white?text=No+Thumbnail',
  videoUrl: video.video_url,
  duration: video.duration || 0,
  views: video.views || 0,
  likes: video.likes || 0,
  isHD: video.is_hd || false,
  category: video.category || 'uncategorized',
  uploadDate: video.created_at,
  creator: {
    id: video.user_id,
    name: video.username || 'Unknown User',
    avatar: video.user_avatar || 'https://placehold.co/150/gray/white?text=User',
    isVerified: false,
    isCreator: true,
    subscriberCount: 0,
  },
});

export const useVideoStoreNew = create<VideoState>((set, get) => ({
  videos: [],
  pagination: {
    currentPage: 1,
    totalPages: 1,
    totalCount: 0,
    pageSize: 20,
  },
  isLoading: false,
  error: null,
  currentCategory: 'all',
  searchQuery: '',

  fetchVideos: async (category = 'all', page = 1) => {
    try {
      set({ isLoading: true, error: null });

      const response = await apiClient.getVideos({
        page,
        limit: 20,
        category: category === 'all' ? undefined : category,
        sort: 'created_at',
        order: 'DESC',
      });

      if (response.success && response.data.videos) {
        const videos = response.data.videos.map(convertApiVideoToVideoItem);
        
        set({
          videos,
          pagination: {
            currentPage: response.data.pagination.page,
            totalPages: response.data.pagination.pages,
            totalCount: response.data.pagination.total,
            pageSize: response.data.pagination.limit,
          },
          currentCategory: category,
          isLoading: false,
        });
      } else {
        throw new Error('Failed to fetch videos');
      }
    } catch (error) {
      console.error('Error fetching videos:', error);
      set({
        error: error instanceof Error ? error.message : 'Failed to fetch videos',
        isLoading: false,
      });
    }
  },

  searchVideos: async (query: string, page = 1) => {
    try {
      set({ isLoading: true, error: null, searchQuery: query });

      const response = await apiClient.getVideos({
        page,
        limit: 20,
        search: query,
        sort: 'created_at',
        order: 'DESC',
      });

      if (response.success && response.data.videos) {
        const videos = response.data.videos.map(convertApiVideoToVideoItem);
        
        set({
          videos,
          pagination: {
            currentPage: response.data.pagination.page,
            totalPages: response.data.pagination.pages,
            totalCount: response.data.pagination.total,
            pageSize: response.data.pagination.limit,
          },
          isLoading: false,
        });
      } else {
        throw new Error('Failed to search videos');
      }
    } catch (error) {
      console.error('Error searching videos:', error);
      set({
        error: error instanceof Error ? error.message : 'Failed to search videos',
        isLoading: false,
      });
    }
  },

  setCategory: (category: string) => {
    const { fetchVideos } = get();
    set({ currentCategory: category, searchQuery: '' });
    fetchVideos(category, 1);
  },

  setPage: async (page: number) => {
    const { currentCategory, searchQuery, fetchVideos, searchVideos } = get();
    
    if (searchQuery) {
      await searchVideos(searchQuery, page);
    } else {
      await fetchVideos(currentCategory, page);
    }
  },

  clearError: () => {
    set({ error: null });
  },
}));
