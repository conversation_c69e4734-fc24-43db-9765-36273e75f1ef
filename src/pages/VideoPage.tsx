import React, { useEffect, useState, Suspense } from 'react';
import { useParams, useSearchParams, useNavigate } from 'react-router-dom';
import SideRecommendations from '../components/sections/SideRecommendations';
import { useVideoStore } from '../stores/videoStore';
import { Video } from '../types';
import { formatDuration, formatCount } from '../utils/formatters';
import { Layers, ChevronRight, ChevronLeft } from 'lucide-react';
import { parseVideoTitle, areTitlesFromSameSeries } from '../utils/videoGrouping';
import OptimizedImage from '../components/ui/OptimizedImage';
import { useVideoNew, useVideosNew } from '../hooks/useVideos';
import NativeVideoPlayer from '../components/video/NativeVideoPlayer';
import SingleVideoLoader from '../components/debug/SingleVideoLoader';

const VideoPage = () => {
  const { id } = useParams();
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const [video, setVideo] = useState<Video | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentVideoIndex, setCurrentVideoIndex] = useState(0);
  const [recommendedVideos, setRecommendedVideos] = useState<Video[]>([]);
  const [seriesVideos, setSeriesVideos] = useState<Video[]>([]);
  const [isPartOfSeries, setIsPartOfSeries] = useState(false);
  const [showSingleVideoLoader, setShowSingleVideoLoader] = useState(false);

  const {
    incrementVideoViews
  } = useVideoStore();

  // Use MySQL API hook for fetching video data
  const {
    video: videoData,
    isLoading: videoLoading,
    error: videoError
  } = useVideoNew(id || '');

  // Use MySQL API hook for fetching recommended videos (most viewed)
  const {
    videos: recommendedVideosData,
    isLoading: recommendedLoading,
    error: recommendedError
  } = useVideosNew('recommended', 1, 20); // Get 20 most viewed videos

  // Update local state when MySQL API data changes
  useEffect(() => {
    if (videoData) {
      // Convert MySQL API video to expected format
      const convertedVideo: Video = {
        id: videoData.id,
        title: videoData.title,
        description: videoData.description || '',
        thumbnail: videoData.thumbnail_url || 'https://placehold.co/400x225/gray/white?text=No+Thumbnail',
        thumbnailUrl: videoData.thumbnail_url || 'https://placehold.co/400x225/gray/white?text=No+Thumbnail',
        videoUrl: videoData.video_url,
        duration: videoData.duration || 0,
        views: videoData.views || 0,
        likes: videoData.likes || 0,
        isHD: videoData.is_hd || false,
        category: videoData.category || 'uncategorized',
        uploadDate: videoData.created_at,
        creator: {
          id: videoData.user_id,
          name: videoData.username || 'Unknown User',
          avatar: videoData.user_avatar || 'https://placehold.co/150/gray/white?text=User',
          isVerified: false,
          isCreator: true,
          subscriberCount: 0,
        },
      };

      setVideo(convertedVideo);
      setLoading(false);
      setShowSingleVideoLoader(false);

      // Increment view count (views are automatically incremented by the API)
      if (id) {
        incrementVideoViews(id);
      }
    } else if (videoError) {
      setError(videoError);
      setLoading(false);
      // Show single video loader if there's an error
      if (videoError.includes('timeout') || videoError.includes('HTTP')) {
        setShowSingleVideoLoader(true);
      }
    } else {
      setLoading(videoLoading);
    }
  }, [videoData, videoLoading, videoError, id, incrementVideoViews]);

  // Show single video loader if loading takes too long
  useEffect(() => {
    if (videoLoading && !videoData && !videoError) {
      const timer = setTimeout(() => {
        console.warn('⚠️ Single video loading taking too long, showing emergency loader');
        setShowSingleVideoLoader(true);
      }, 8000); // Show after 8 seconds

      return () => clearTimeout(timer);
    }
  }, [videoLoading, videoData, videoError]);

  // Handle emergency video loading
  const handleEmergencyVideoLoad = (loadedVideo: Video) => {
    setVideo(loadedVideo);
    setLoading(false);
    setError(null);
    setShowSingleVideoLoader(false);
    console.log('✅ Emergency single video load successful:', loadedVideo.title);
  };

  // Process recommended videos and identify series videos
  useEffect(() => {
    const processRecommendedVideos = () => {
      if (!id || !video || !recommendedVideosData) return;

      try {
        // Convert MySQL API videos to expected format
        let videos: Video[] = recommendedVideosData.map(apiVideo => ({
          id: apiVideo.id,
          title: apiVideo.title,
          description: apiVideo.description || '',
          thumbnail: apiVideo.thumbnail_url || 'https://placehold.co/400x225/gray/white?text=No+Thumbnail',
          thumbnailUrl: apiVideo.thumbnail_url || 'https://placehold.co/400x225/gray/white?text=No+Thumbnail',
          videoUrl: apiVideo.video_url,
          duration: apiVideo.duration || 0,
          views: apiVideo.views || 0,
          likes: apiVideo.likes || 0,
          isHD: apiVideo.is_hd || false,
          category: apiVideo.category || 'uncategorized',
          uploadDate: apiVideo.created_at,
          creator: {
            id: apiVideo.user_id,
            name: apiVideo.username || 'Unknown User',
            avatar: apiVideo.user_avatar || 'https://placehold.co/150/gray/white?text=User',
            isVerified: false,
            isCreator: true,
            subscriberCount: 0,
          },
        }));

        // Check if current video is part of a series
        const currentVideoTitle = video.title;
        const parsedTitle = parseVideoTitle(currentVideoTitle);

        if (parsedTitle) {
          // Find other videos in the same series
          const seriesVids = videos.filter(v =>
            v.id !== id && areTitlesFromSameSeries(v.title, currentVideoTitle)
          );

          if (seriesVids.length > 0) {
            setIsPartOfSeries(true);

            // Sort series videos by part number
            const sortedSeriesVideos = [...seriesVids].sort((a, b) => {
              const parsedA = parseVideoTitle(a.title);
              const parsedB = parseVideoTitle(b.title);

              if (parsedA && parsedB) {
                return parsedA.partNumber - parsedB.partNumber;
              }
              return 0;
            });

            // Add the current video to the series
            const allSeriesVideos = [video, ...sortedSeriesVideos].sort((a, b) => {
              const parsedA = parseVideoTitle(a.title);
              const parsedB = parseVideoTitle(b.title);

              if (parsedA && parsedB) {
                return parsedA.partNumber - parsedB.partNumber;
              }
              return 0;
            });

            setSeriesVideos(allSeriesVideos);

            // Find the index of the current video in the series
            const currentIndex = allSeriesVideos.findIndex(v => v.id === id);
            if (currentIndex !== -1) {
              setCurrentVideoIndex(currentIndex);
            }

            // Remove series videos from recommendations
            videos = videos.filter(v => !areTitlesFromSameSeries(v.title, currentVideoTitle));
          }
        }

        // Filter out the current video
        const filteredVideos = videos.filter(v => v.id !== id);
        setRecommendedVideos(filteredVideos);
      } catch (error) {
        console.error('Error processing recommended videos:', error);
      }
    };

    processRecommendedVideos();
  }, [id, video, recommendedVideosData]);

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-700"></div>
        </div>
      </div>
    );
  }

  if (error || !video) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="bg-red-500/20 border border-red-500 rounded-md p-4 max-w-2xl mx-auto">
          <h1 className="text-2xl font-bold text-white mb-2">Video not found</h1>
          {error && <p className="text-red-200">{error}</p>}
        </div>
      </div>
    );
  }

  // Navigation functions for series videos
  const goToNextVideo = () => {
    if (isPartOfSeries && seriesVideos.length > 0 && currentVideoIndex < seriesVideos.length - 1) {
      // Series navigation
      const nextVideo = seriesVideos[currentVideoIndex + 1];
      navigate(`/video/${nextVideo.id}`);
    }
  };

  const goToPreviousVideo = () => {
    if (isPartOfSeries && seriesVideos.length > 0 && currentVideoIndex > 0) {
      // Series navigation
      const prevVideo = seriesVideos[currentVideoIndex - 1];
      navigate(`/video/${prevVideo.id}`);
    }
  };

  return (
    <div className="container mx-auto px-4 relative">
      {/* Single Video Emergency Loader */}
      <SingleVideoLoader
        videoId={id || ''}
        onVideoLoaded={handleEmergencyVideoLoad}
        isVisible={showSingleVideoLoader}
      />

      <div className="flex flex-col lg:flex-row gap-4">
        {/* Main video content - takes more space */}
        <div className="lg:w-3/4">
          {/* Use the enhanced NativeVideoPlayer component */}
          <NativeVideoPlayer
            video={video}
            onBack={() => navigate(-1)}
          />

          {/* Video title and metadata - mobile optimized */}
          <div className="mt-3 mb-4 sm:mt-4 sm:mb-6 px-2 sm:px-0">
            <h1 className="text-lg sm:text-xl md:text-2xl font-bold text-white mb-2 leading-tight">{video.title}</h1>
            <div className="flex items-center flex-wrap gap-2 sm:gap-4 text-gray-400 text-xs sm:text-sm">
              <span>{formatCount(video.views)} views</span>
              {video.duration && <span>{formatDuration(video.duration)}</span>}
              {video.isHd && (
                <span className="bg-blue-700 text-white px-2 py-1 rounded text-xs font-medium">HD</span>
              )}
            </div>
          </div>

          {/* Series videos navigation - mobile optimized */}
          {isPartOfSeries && seriesVideos.length > 1 && (
            <div className="mt-4 mb-6 sm:mb-8 px-2 sm:px-0">
              <div className="bg-gray-800 rounded-lg p-3 sm:p-4">
                <div className="flex items-center justify-between mb-3 sm:mb-4">
                  <div className="flex items-center min-w-0 flex-1">
                    <Layers size={16} className="text-blue-700 mr-2 flex-shrink-0 sm:w-5 sm:h-5" />
                    {parseVideoTitle(video?.title || '') && (
                      <h3 className="text-sm sm:text-lg font-medium text-white truncate">
                        {parseVideoTitle(video?.title || '')?.baseTitle} ({currentVideoIndex + 1}/{seriesVideos.length})
                      </h3>
                    )}
                  </div>
                  <div className="flex space-x-1 sm:space-x-2 flex-shrink-0">
                    <button
                      className={`p-1.5 sm:p-2 rounded-full ${
                        currentVideoIndex > 0
                          ? 'bg-gray-700 hover:bg-gray-600 text-white'
                          : 'bg-gray-700 text-gray-500 cursor-not-allowed'
                      }`}
                      onClick={goToPreviousVideo}
                      disabled={currentVideoIndex === 0}
                      aria-label="Previous video"
                    >
                      <ChevronLeft size={16} className="sm:w-5 sm:h-5" />
                    </button>
                    <button
                      className={`p-1.5 sm:p-2 rounded-full ${
                        currentVideoIndex < seriesVideos.length - 1
                          ? 'bg-gray-700 hover:bg-gray-600 text-white'
                          : 'bg-gray-700 text-gray-500 cursor-not-allowed'
                      }`}
                      onClick={goToNextVideo}
                      disabled={currentVideoIndex === seriesVideos.length - 1}
                      aria-label="Next video"
                    >
                      <ChevronRight size={16} className="sm:w-5 sm:h-5" />
                    </button>
                  </div>
                </div>

                <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-2 overflow-x-auto">
                  {seriesVideos.map((seriesVideo, index) => {
                    const parsedTitle = parseVideoTitle(seriesVideo.title);
                    return (
                      <div
                        key={seriesVideo.id}
                        className={`relative cursor-pointer rounded-md overflow-hidden ${
                          index === currentVideoIndex ? 'ring-2 ring-blue-700' : ''
                        }`}
                        onClick={() => navigate(`/video/${seriesVideo.id}`)}
                      >
                        <OptimizedImage
                          src={seriesVideo.thumbnailUrl}
                          alt={seriesVideo.title}
                          fallbackSrc="https://via.placeholder.com/640x360?text=No+Thumbnail"
                          className="w-full aspect-video object-cover"
                          width={320}
                          height={180}
                          sizes="(max-width: 640px) 50vw, 20vw"
                          priority={index < 3}
                          progressive={true}
                          enableModernFormats={true}
                          fetchPriority={index < 3 ? "high" : "low"}
                        />
                        <div className="absolute inset-0 bg-black bg-opacity-40 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity">
                          <span className="text-white text-sm font-medium">
                            {parsedTitle ? `Part ${parsedTitle.partNumber}` : `Part ${index + 1}`}
                          </span>
                        </div>
                        {index === currentVideoIndex && (
                          <div className="absolute bottom-0 left-0 right-0 bg-blue-700 h-1"></div>
                        )}
                      </div>
                    );
                  })}
                </div>
              </div>
            </div>
          )}

          {/* Mobile recommendations - only visible on mobile */}
          <div className="block lg:hidden mt-4 mb-8 px-2 sm:px-0">
            {recommendedVideos.length > 0 && (
              <div className="bg-gray-900 rounded-lg p-3 sm:p-4">
                <h3 className="text-base sm:text-lg font-medium text-white mb-3 sm:mb-4">Recommended Videos</h3>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 sm:gap-3">
                  {recommendedVideos.slice(0, 6).filter(v => v.id !== video.id).map(video => (
                    <div
                      key={video.id}
                      className="cursor-pointer hover:bg-gray-800 rounded-lg transition-colors p-1.5 sm:p-2 group"
                      onClick={() => navigate(`/video/${video.id}`)}
                    >
                      <div className="relative w-full">
                        <OptimizedImage
                          src={video.thumbnailUrl}
                          alt={video.title}
                          fallbackSrc="https://via.placeholder.com/640x360?text=No+Thumbnail"
                          className="w-full aspect-video object-cover rounded-md"
                          width={320}
                          height={180}
                          sizes="(max-width: 640px) 50vw, 33vw"
                        />
                        <div className="absolute bottom-1 right-1 bg-black bg-opacity-70 text-white text-xs px-1 rounded">
                          {formatDuration(video.duration)}
                        </div>
                        <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-opacity flex items-center justify-center">
                          <div className="w-10 h-10 rounded-full bg-blue-700/80 text-white flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                              <polygon points="5 3 19 12 5 21 5 3"></polygon>
                            </svg>
                          </div>
                        </div>
                      </div>
                      <div className="mt-1.5 sm:mt-2">
                        <h4 className="text-white text-xs sm:text-sm font-medium line-clamp-2 group-hover:text-blue-400 transition-colors leading-tight">{video.title}</h4>
                        <div className="text-gray-400 text-xs mt-0.5 sm:mt-1">
                          {formatCount(Number(video.views) || 0)} views
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Side recommendations - only visible on desktop */}
        <div className="hidden lg:block lg:w-1/4 mt-4 lg:mt-0">
          {recommendedVideos.length > 0 && (
            <SideRecommendations
              videos={recommendedVideos}
              currentVideoId={video.id}
            />
          )}
        </div>
      </div>
    </div>
  );
};

export default VideoPage;