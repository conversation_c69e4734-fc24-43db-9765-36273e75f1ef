/**
 * New Video Hooks using MySQL API
 * Replaces Supabase-based video hooks
 */

import { useState, useEffect, useCallback } from 'react';
import { apiClient, Video } from '../lib/api';

// Hook for fetching videos with pagination
export function useVideosNew(
  category: string = 'all',
  page: number = 1,
  limit: number = 20
) {
  const [videos, setVideos] = useState<Video[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState({
    currentPage: 1,
    totalPages: 1,
    totalCount: 0,
    pageSize: limit,
  });

  const fetchVideos = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await apiClient.getVideos({
        page,
        limit,
        category: category === 'all' ? undefined : category,
        sort: 'created_at',
        order: 'DESC',
      });

      if (response.success && response.data.videos) {
        setVideos(response.data.videos);
        setPagination({
          currentPage: response.data.pagination.page,
          totalPages: response.data.pagination.pages,
          totalCount: response.data.pagination.total,
          pageSize: response.data.pagination.limit,
        });
      } else {
        throw new Error('Failed to fetch videos');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch videos';
      setError(errorMessage);
      console.error('Error fetching videos:', err);
    } finally {
      setIsLoading(false);
    }
  }, [category, page, limit]);

  useEffect(() => {
    fetchVideos();
  }, [fetchVideos]);

  const refetch = useCallback(() => {
    fetchVideos();
  }, [fetchVideos]);

  return {
    videos,
    isLoading,
    error,
    pagination,
    refetch,
  };
}

// Hook for fetching a single video by ID
export function useVideoNew(id: string) {
  const [video, setVideo] = useState<Video | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchVideo = useCallback(async () => {
    if (!id) return;

    try {
      setIsLoading(true);
      setError(null);

      const response = await apiClient.getVideo(id);

      if (response.success && response.data) {
        setVideo(response.data);
      } else {
        throw new Error('Video not found');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch video';
      setError(errorMessage);
      console.error('Error fetching video:', err);
    } finally {
      setIsLoading(false);
    }
  }, [id]);

  useEffect(() => {
    fetchVideo();
  }, [fetchVideo]);

  const refetch = useCallback(() => {
    fetchVideo();
  }, [fetchVideo]);

  return {
    video,
    isLoading,
    error,
    refetch,
  };
}

// Hook for searching videos
export function useVideoSearchNew(query: string, page: number = 1, limit: number = 20) {
  const [videos, setVideos] = useState<Video[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState({
    currentPage: 1,
    totalPages: 1,
    totalCount: 0,
    pageSize: limit,
  });

  const searchVideos = useCallback(async () => {
    if (!query.trim()) {
      setVideos([]);
      setPagination({
        currentPage: 1,
        totalPages: 1,
        totalCount: 0,
        pageSize: limit,
      });
      return;
    }

    try {
      setIsLoading(true);
      setError(null);

      const response = await apiClient.getVideos({
        page,
        limit,
        search: query,
        sort: 'created_at',
        order: 'DESC',
      });

      if (response.success && response.data.videos) {
        setVideos(response.data.videos);
        setPagination({
          currentPage: response.data.pagination.page,
          totalPages: response.data.pagination.pages,
          totalCount: response.data.pagination.total,
          pageSize: response.data.pagination.limit,
        });
      } else {
        throw new Error('Search failed');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Search failed';
      setError(errorMessage);
      console.error('Error searching videos:', err);
    } finally {
      setIsLoading(false);
    }
  }, [query, page, limit]);

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      searchVideos();
    }, 300); // Debounce search

    return () => clearTimeout(timeoutId);
  }, [searchVideos]);

  return {
    videos,
    isLoading,
    error,
    pagination,
    refetch: searchVideos,
  };
}

// Hook for fetching categories
export function useCategoriesNew() {
  const [categories, setCategories] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchCategories = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await apiClient.getCategories();

      if (response.success && response.data) {
        setCategories(response.data);
      } else {
        throw new Error('Failed to fetch categories');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch categories';
      setError(errorMessage);
      console.error('Error fetching categories:', err);
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchCategories();
  }, [fetchCategories]);

  return {
    categories,
    isLoading,
    error,
    refetch: fetchCategories,
  };
}
