{"name": "bluefilmx-streaming-platform", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "postbuild": "cp dist/index.html dist/404.html && npm run generate-version", "generate-version": "node scripts/generate-version.js", "lint": "eslint .", "preview": "vite preview", "vercel-build": "vite build && npm run postbuild", "deploy": "npx vercel --prod", "deploy:namecheap": "./scripts/deploy-namecheap.sh", "migrate:storage": "node scripts/migrate-storage.js", "test": "vitest run", "clear-cache": "node scripts/clear-cache.js"}, "dependencies": {"@supabase/supabase-js": "^2.39.7", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-window": "^1.8.8", "lucide-react": "^0.344.0", "react": "^18.3.1", "react-beautiful-dnd": "^13.1.1", "react-dom": "^18.3.1", "react-intersection-observer": "^9.16.0", "react-router-dom": "^6.22.3", "react-virtualized-auto-sizer": "^1.0.26", "react-window": "^1.8.11", "swr": "^2.3.3", "zustand": "^4.5.2"}, "devDependencies": {"@eslint/js": "^9.9.1", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "jsdom": "^26.1.0", "postcss": "^8.4.35", "rollup-plugin-visualizer": "^5.14.0", "tailwindcss": "^3.4.1", "terser": "^5.39.0", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vercel": "^41.6.2", "vite": "^5.4.2", "vitest": "^3.1.3"}}